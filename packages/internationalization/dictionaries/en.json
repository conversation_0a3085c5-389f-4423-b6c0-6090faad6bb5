{"admin": {"title": "Senders Admin Panel", "description": "Manage return, exchange, and address change requests", "header": "Senders Admin Panel", "footer": "© {year} Senders Return Portal. All rights reserved.", "sidebar": {"return_requests": "Return Requests", "address_changes": "Address Changes", "receipt_customizations": "Receipt Customizations", "users": "Users"}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "search": "Search...", "filter": "Filter", "sort": "Sort", "actions": "Actions", "status": "Status", "date": "Date", "no_results": "No results found", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "processing": "Processing...", "beta_feature": "Beta feature now available", "yes": "Yes", "no": "No", "home": "Home", "summary": "Summary"}, "auth": {"sign_in_title": "Welcome back", "sign_in_description": "Enter your details to sign in.", "sign_in_prompt": "Already have an account?", "sign_in_error": "Invalid email or password", "sign_up_title": "Create an account", "sign_up_description": "Create an account to get started.", "sign_up_prompt": "Don't have an account?", "sign_up_error": "Failed to sign up", "sign_in": "Sign In", "sign_up": "Sign Up", "sign_out": "Sign Out", "name": "Name", "email": "Email", "password": "Password", "forgot_password": "Forgot password?", "remember_me": "Remember me", "terms_agreement": "By clicking continue, you agree to our Terms of Service and Privacy Policy."}, "return_requests": {"title": "Return Requests", "description": "Manage customer return requests", "order_name": "Order Number", "email": "Email", "return_number": "Return Number", "return_reason": "Return Reason", "exchange_type": "Exchange Type", "status": "Status", "processed": "Processed", "created_at": "Created At", "updated_at": "Updated At", "return_items": "Return Items", "defect_photos": "Defect Photos", "details": "Details", "process": "Process", "mark_as_processed": "<PERSON> as Processed", "confirm_process": "Are you sure you want to mark this return request as processed?", "process_success": "Return request has been successfully processed", "delete_confirmation": "Delete Return Request", "delete_description": "Are you sure you want to delete this return request? This action cannot be undone.", "delete_button": "Delete Return Request", "deleting": "Deleting...", "update_status": "Update Status", "update_status_title": "Update Return Request Status", "update_status_description": "Update the status and processing information for this return request.", "select_status": "Select a status", "status_pending": "Pending", "status_approved": "Approved", "status_rejected": "Rejected", "status_completed": "Completed", "status_description": "The current status of this return request.", "processed_description": "Mark this return as processed when complete.", "admin_notes": "Admin Notes", "admin_notes_placeholder": "Add notes about this return request", "admin_notes_description": "These notes are only visible to administrators."}, "address_changes": {"title": "Address Changes", "description": "Manage customer address change requests", "order_name": "Order Number", "email": "Email", "status": "Status", "created_at": "Created At", "updated_at": "Updated At", "details": "Details", "original_address": "Original Address", "new_address": "New Address", "update_status": "Update Status", "status_updated": "Status has been successfully updated", "delete_confirmation": "Delete Address Change", "delete_description": "Are you sure you want to delete this address change request? This action cannot be undone.", "delete_button": "Delete Address Change", "deleting": "Deleting..."}, "receipt_customizations": {"title": "Receipt Customizations", "description": "Manage customer receipt customizations", "order_name": "Order Number", "email": "Email", "recipient_name": "Recipient Name", "company_name": "Company Name", "download_count": "Download Count", "last_downloaded": "Last Downloaded", "created_at": "Created At", "updated_at": "Updated At", "details": "Details", "delete_confirmation": "Delete Receipt Customization", "delete_description": "Are you sure you want to delete this receipt customization? This action cannot be undone.", "delete_button": "Delete Receipt Customization", "deleting": "Deleting..."}, "users": {"title": "Users", "description": "Manage system users", "name": "Name", "email": "Email", "role": "Role", "created_at": "Created At", "updated_at": "Updated At", "details": "Details", "first_name": "First Name", "last_name": "Last Name", "delete_confirmation": "Delete User", "delete_description": "Are you sure you want to delete this user? This action cannot be undone.", "delete_button": "Delete User", "deleting": "Deleting..."}, "error": {"title": "Something went wrong", "description": "There was a problem. Please try again.", "try_again": "Try again"}}, "site": {"title": "Shopify Order Return & Exchange Portal", "description": "Process your returns and exchanges quickly and easily", "header": "Senders Return Portal", "footer": "© {year} Senders Return Portal. All rights reserved."}, "nav": {"home": "Home", "orders": "Orders", "returns": "Returns", "account": "Account", "language": "Language"}, "form": {"email": "Email", "order": "Order Number", "submit": "Submit", "cancel": "Cancel", "required": "is required", "email_invalid": "Please enter a valid email address", "find_order": "Find Your Order", "find_order_button": "Find Order", "enter_details": "Enter your Order Number and the email address used for your purchase to begin.", "order_placeholder": "Enter order number", "email_placeholder": "Enter your email address", "help_text": "You can find your Order Number in your order confirmation email. For security, we verify your email matches the order."}, "message": {"success": "Success!", "error": "An error occurred while verifying your order", "loading": "Loading...", "verifying": "Verifying...", "order_not_found": "Order not found or email doesn't match", "unexpected_error": "An unexpected error occurred. Please try again.", "no_submission": "We cannot process your return without all original packaging and accessories.", "return_only": "For this reason, we can only process a return, not an exchange.", "cannot_return": "Sorry, we cannot process a return or exchange with this reason.", "verification_failed": "Email verification failed. Please enter the email address associated with this order."}, "alert": {"address_updated_title": "Address Updated", "address_updated_message": "Your shipping address has been successfully updated. Please check your Shopify order confirmation page to verify the address change has been applied correctly.", "receipt_downloaded_title": "Receipt Downloaded", "receipt_downloaded_message": "Your receipt has been successfully downloaded.", "return_processed_title": "Return Processed", "return_processed_message": "Your return request has been successfully processed.", "delivery_confirmed_title": "Delivery Confirmed", "delivery_confirmed_message": "Your delivery date has been confirmed.", "success_title": "Success", "success_message": "Your request has been successfully processed."}, "date": {"format": "MMMM d, yyyy"}, "currency": {"format": "USD"}, "order": {"information": "Order Information", "number": "Order Number", "purchase_date": "Purchase Date", "select_items": "Select Items to Return/Exchange", "quantity": "Quantity", "return_exchange": "Order Return/Exchange"}, "return": {"what_to_do": "What would you like to do?", "generating_label": "Generating Label...", "download_label": "Download Return Label", "download_label_description": "Download the return label to print and attach to your package", "size_exchange": "Size Exchange", "item_exchange": "Other Item Exchange", "exchange": "Exchange", "return": "Return", "return_item": "Return Item", "view_order": "View Order", "view_order_description": "View your order details and download receipt on Shopify", "reason_title": "Why are you returning/exchanging these items?", "reason_placeholder": "Please provide a reason for your return or exchange...", "inquiry_type": "Inquiry Type", "select_service": "Please select a service from the options below", "returns_exchanges": "Returns and Exchanges", "returns_exchanges_description": "Process a return or exchange for your order", "download_receipt": "Download Receipt", "download_receipt_description": "Download a receipt for your order", "change_address": "Change of Address", "change_address_description": "Update your shipping address", "delivery_confirmation": "Delivery Date Confirmation", "delivery_confirmation_description": "Check your delivery status", "selected_items": "Selected Items for Return", "defective_options": "Defective Product Options", "wrong_product_options": "Wrong Product Options", "exchange_question": "Would you like to return this item for a refund or exchange it for a replacement?", "exchange_replacement": "Exchange for a replacement", "return_refund": "Return for a refund", "you_selected": "You selected:", "change_selection": "Change selection", "wrong_product_details": "Wrong Product Details", "selected_option": "Selected Option:", "exchange_correct_product": "Exchange for correct product", "return_for_refund": "Return for refund", "return_details": "Return Details", "review_exchange": "Review Your Exchange", "review_return": "Review Your Return", "return_label": "Return Label:", "type": "Type:", "understand_condition": "I understand that I need to return the items in their original condition with all tags and packaging.", "agree_policy": "I have read and agree to the return policy.", "request_submitted": "Return Request Submitted", "thank_you": "Thank you for your return request!", "request_success": "Your return request has been submitted successfully.", "return_number": "Return Number:", "items_returned": "Items Being Returned", "next_steps": "Next Steps:", "email_confirmation": "You will receive a confirmation email shortly.", "package_securely": "Please package your items securely with all original packaging and tags.", "print_label": "Print the return label that will be included in the email.", "drop_off": "Drop off the package at your nearest postal service location.", "refund_processed": "Your refund will be processed once we receive and inspect the returned items.", "questions_contact": "If you have any questions about your return, please contact our customer service team and reference your return number.", "done": "Done", "return_request": "Return Request", "step_of": "Step {step} of 5", "processing": "Processing...", "submit_request": "Submit Request", "next": "Next", "reason_label": "Return Reason:", "total_items": "Total Items:", "total_amount": "Total Amount:", "add_photos": "Add Photos", "max_photos_reached": "Maximum {max} photos reached", "request_rejected": "Return Request Rejected", "rejection_message": "We're sorry, but we cannot accept your return request for the following reason:", "expired_message": "This order is more than {days} days old and is no longer eligible for return or exchange.", "expired_policy": "Our return policy allows returns within {days} days of delivery. Unfortunately, this order has exceeded that timeframe.", "special_approval_message": "This order is more than 10 days old. Returns after 10 days require special approval, which we are granting as an exception.", "refund_fee_message": "A refund fee of {fee} will be charged for this return reason.", "return_period": "Return period is calculated from the delivery date", "already_processed_summary": "Some items in this order have already been processed for return or exchange and cannot be returned again.", "checking_request": "We are checking your request...", "please_wait": "Please wait a moment...", "label_options": "Return Label Options", "print_label_yourself": "Print the return label yourself", "request_envelope": "Request a return envelope from us", "envelope_message": "We'll send you a return envelope to the address on your order. Please allow 3-5 business days for delivery.", "return_instructions": "Return Instructions", "not_available": "Return Not Available", "submission_error": "There was an error submitting your return request. Please try again.", "already_processed": "These items have already been processed for return or exchange.", "already_processed_details": "Our system shows that these items have already been processed for a return or exchange. Each item can only be returned once.", "already_rejected": "These items have already been rejected for return.", "already_pending": "These items already have a pending return request.", "already_approved": "These items already have an approved return request.", "already_completed": "These items have already been returned.", "pending_details": "Your return request is currently being processed. Please check back later for updates.", "approved_details": "Your return request has been approved. Please follow the instructions provided in your email.", "completed_details": "These items have already been successfully returned and processed.", "delivery_date": "Delivery Date: ", "order_date": "Order Date: ", "return_period_calculation": "Return period is calculated from the delivery date", "need_assistance": "Need assistance?", "assistance_message": "If you believe this is an error or have questions about our return policy, please contact our customer support team for assistance.", "return_not_allowed": "Returns/exchanges are not allowed until all items are delivered.", "reason": {"title": "Why?", "wrong_size_model": "Ordered the wrong size or model", "defect": "Product malfunction or initial defect", "different_description": "Different from the website description", "wrong_product": "Received a different product than ordered", "different_image": "Different from what I imagined", "no_longer_needed": "No longer needed due to personal circumstances", "customer_damage": "Product damaged, scratched, or soiled while in customer's possession"}, "accessories": {"title": "Do you have your box and other included items like tags and cards?", "yes": "Yes", "no": "No"}, "defect": {"title": "Details of defect", "broken": "Product is broken", "scratched": "Product has scratches/damage", "dirt": "Dirt/stains", "other": "Other", "details_prompt": "Tell us the details of defect (30 words limitation)", "photo_prompt": "Attach the photo of defect", "other_reason_prompt": "Please provide specific details", "photo_required": "Please upload at least one photo of the defect"}, "exchange_type": {"title": "Type exchange or cancel", "exchange": "Exchange", "return": "Return"}, "how_to_exchange": {"title": "How to exchange", "description": "We will send you the exchange item and a return envelope. Please return your item using the following method.", "about_return": "About Return", "return_instructions": "After you receive and confirm the exchange product, please return your current item. Place it in the enclosed return envelope and drop it in a postal mailbox.", "important_notice": "Important Notice", "return_period": "Due to an increase in unreturned items, we have established a return period. The return period is within 2 weeks after receiving the exchange product. If we do not receive your return within this period, we will contact you. Thank you for your understanding."}, "how_to_return": {"title": "How to return?", "instructions": ["Please place the product along with all included items such as manuals and cables in the original packaging. Please note that returns cannot be accepted if any included items are missing.", "Please write the return number ({returnNumber}) on the outside of the box.", "The product return label includes the return address and recipient information, so please print it and attach it to the envelope.", "Please drop it off at your nearest mailbox within 7 business days.", "Once we receive the product, we will process your refund."]}, "refund": {"title": "About Refund", "credit_card": {"title": "Credit Card", "instructions": ["Refunds will be processed according to the procedures set by your credit card company. Due to the billing cycle of your card company, the purchase amount may be charged once, and the refund may be processed in the following month or later.", "Refund methods and dates vary depending on your credit card company. For details about your refund, please contact your credit card company directly.", "(Due to privacy protection policies, we cannot obtain detailed information on your behalf.)"]}, "other_payment": {"title": "au Pay, Mercari Pay, PayPay", "instructions": ["We will cancel the payment for the returned product through each payment method. Due to the billing cycle of your card company, the purchase amount may be charged once, and the refund may be processed in the following month or later.", "Refund methods and dates vary depending on your card company. For details about your refund, please contact each payment service provider and your card company directly.", "(Due to privacy protection policies, we cannot obtain detailed information on your behalf.)"]}}}, "button": {"continue": "Continue", "cancel": "Cancel", "back": "Back", "submit": "Submit", "upload": "Upload Photo", "print": "Print Return Label", "download": "Download Receipt", "view_order": "View Order"}, "address": {"line1": "Address Line 1", "line2": "Address Line 2", "city": "City", "state": "State/Prefecture", "postal": "Postal Code", "country": "Country", "original_shipping": "Original Shipping Address:", "new_shipping": "New Shipping Address:", "enter_new": "Enter new address...", "city_placeholder": "City", "state_placeholder": "State", "postal_placeholder": "Postal Code", "country_placeholder": "Country", "enter_details": "Enter New Address Details:", "first_name": "First Name", "first_name_required": "First Name is required", "last_name": "Last Name", "last_name_required": "Last Name is required", "company_optional": "Company (Optional)", "phone_optional": "Phone (Optional)", "country_required": "Country is required", "state_required": "State/Prefecture is required", "select_country": "Select Country...", "search_country": "Search country...", "no_country": "No country found.", "select_state": "Select State...", "search_state": "Search state...", "no_state": "No state found.", "confirm_title": "Confirm Address Change", "confirm_description": "Are you sure you want to update the shipping address for this order?", "original_address": "Original Address:", "new_address": "New Address:", "cancel": "Cancel", "processing": "Processing...", "confirm_change": "Confirm Change", "change_not_allowed": "Address Change Not Allowed", "not_allowed_message": "Address change is not allowed at this time.", "tracking_number_message": "Address change is not allowed because the order has a tracking number.", "fulfilled_message": "Address change is not allowed because the order has been fulfilled.", "not_allowed_generic": "Address change is not allowed at this time.", "error_checking": "An error occurred while checking if address change is allowed", "checking_status": "Checking if address change is allowed...", "back_to_services": "Back to Services", "line1_required": "Address Line 1 is required", "city_required": "City is required", "postal_required": "Postal Code is required", "country_locked": "Country cannot be modified"}, "delivery": {"delivered": "Delivered", "in_transit": "In Transit", "estimated": "Estimated delivery", "tracking": "Tracking Information", "carrier": "Carrier", "tracking_number": "Tracking Number", "history": "Delivery History", "not_available": "N/A", "invalid_date": "Invalid date", "no_information": "No delivery information is available for this order yet.", "attempted": "Delivery Attempted", "failed": "Delivery Failed", "processing": "Processing", "preparing_for_delivery": "Preparing for Delivery", "cancelled": "Cancelled", "pending": "Pending", "track_package": "Track Package", "confirm_delivery": "Confirm Delivery", "partially_fulfilled": "Partially Fulfilled", "multiple_shipments": "Multiple Shipments", "shipment": "Shipment", "shipment_number": "Shipment #{number}", "fulfillment_summary": "Fulfillment Summary", "items_shipped": "{shipped} of {total} items shipped", "remaining_items": "Remaining items will be shipped separately", "all_items_delivered": "All items have been delivered", "some_items_delivered": "Some items have been delivered", "created_on": "Created on", "updated_on": "Updated on"}, "common": {"drop_image": "Drop your image here or click to browse", "max_size": "Max size: {maxSize}MB", "required": "Required", "switch_language": "Switch language", "failed_to_load": "Failed to load content", "how_to_exchange": "How to Exchange", "how_to_return": "How to Return", "about_refund": "About Refund", "no_information_available": "No information available about {title}."}, "receipt": {"dialog": {"title": "Generate Receipt", "description": "Customize the recipient details for your receipt.", "back": "Back", "order_title": "Receipt for Order {orderName}", "view_pdf": "View PDF", "error_message": "There was an error downloading the PDF. Please try again.", "checking_fulfillment": "Checking order status...", "not_fulfilled": "This order has not been fulfilled yet. You can download the receipt after the order has been fulfilled.", "close": "Close", "confirm_title": "Confirm Receipt Customization", "confirm_description": "Are you sure you want to save these changes? You will not be able to edit the receipt again after this.", "confirm_warning": "This action cannot be undone. Once you save these customizations, you will not be able to modify the receipt details again.", "cancel": "Cancel", "confirm_save": "Confirm & Save", "already_customized": "This receipt has already been customized and cannot be edited again.", "save_error": "Failed to save receipt customization", "save_error_generic": "An error occurred while saving the customization", "pdf_error": "There was an error generating the PDF. Please try again."}, "form": {"recipient_name": "Recipient/Company Name", "recipient_placeholder": "Casefinite Inc.", "recipient_description": "The name that will appear on the receipt", "note_name": "Note", "note_placeholder": "For the cost of goods", "note_description": "Input example: “As payment for goods”.", "generate": "Generate Receipt", "recipient_required": "Recipient name is required"}, "pdf": {"store_name": "CASEFINIT Inc.", "store_zip": "105-0011", "store_address": "1-8-20 <PERSON><PERSON>koen, Minato-ku, Tokyo", "store_address2": "", "store_phone": "", "merchant_id": "T7013301046118", "invoice": "Receipt", "already_printed_out": "(Reissued)", "order_number": "Order Number", "processed_at": "Order Date", "shipping_address": "Shipping Information", "customer_information": "Customer Information", "billing_address": "Order Date", "phone": "TEL", "payment_method": "Payment Method", "shipping_method": "Shipping Method", "products": "Products", "price_single": "Unit Price", "quantity": "Quantity", "price_total": "Total", "total_discounts": "Discounts", "subtotal_price": "Subtotal", "shipping_price": "Shipping", "total_price": "Total Payment", "total_refunds": "Total Refunds", "recipient_suffix": "", "tax_included": "(Tax included)", "for_products": "For products", "receipt_confirmation": "We have received the above amount.", "reduced_tax_note": "※ Items subject to reduced tax rate", "tax": "Tax", "breakdown": "Breakdown", "tax_rate_subject": "{rate}% subject", "consumption_tax": "Consumption Tax ({rate}%)", "special_notes": "Special Notes", "credit_card_note_1": "※ In the case of credit card payment, since there is no fact of receiving money,", "credit_card_note_2": "　even if the title is \"Receipt\", it does not correspond to Document No. 17-1 of the Stamp Tax Law Basic Circular.", "credit_card_note_3": "※ No revenue stamp is affixed even if it exceeds 50,000 yen."}}}