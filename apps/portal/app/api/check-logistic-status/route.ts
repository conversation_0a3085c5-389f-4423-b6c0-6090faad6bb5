import { env } from '@/env';
import { getLogisticsStatus } from '@/lib/logistics';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName }: { orderName: string } = await request.json();

    log.info(`Checking logistic status for order ${orderName}`);

    if (!orderName) {
      return NextResponse.json(
        { error: 'Order name are required' },
        { status: 400 }
      );
    }

    // Get environment (Japan or global)
    const isJapanEnv = env.NEXT_PUBLIC_REGION === 'JP';

    if (!isJapanEnv) {
      return NextResponse.json({}, { status: 200 });
    }

    const logisticStatus = await getLogisticsStatus(orderName);
    log.info(`${orderName} logistic status`, { logisticStatus });

    return NextResponse.json(
      {
        logisticStatus,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error checking logistic status:', { error });
    return NextResponse.json(
      {
        error: 'An error occurred while checking logistic status',
      },
      { status: 500 }
    );
  }
}
