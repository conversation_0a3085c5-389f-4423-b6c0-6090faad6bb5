import { createReceiptCustomization } from '@/lib/db';
import { verifyOrderAccess } from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

interface ReceiptCustomizationData {
  orderName: string;
  email: string;
  recipientName?: string;
  note?: string;
}

export async function POST(request: NextRequest) {
  try {
    const data = (await request.json()) as ReceiptCustomizationData;
    const { orderName, email, recipientName, note } = data;

    log.info(`Saving receipt customization for order ${orderName}`);

    if (!orderName || !email) {
      return NextResponse.json(
        { error: 'Order name and email are required' },
        { status: 400 }
      );
    }

    // Verify the order with email
    const order = await verifyOrderAccess(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
        },
        { status: 200 }
      );
    }

    // Save the receipt customization to the database
    const savedCustomization = await createReceiptCustomization({
      orderName,
      email,
      recipientName,
      note,
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Receipt customization saved successfully',
        customization: savedCustomization,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error saving receipt customization:', { error });
    return NextResponse.json(
      { error: 'An error occurred while saving receipt customization' },
      { status: 500 }
    );
  }
}
