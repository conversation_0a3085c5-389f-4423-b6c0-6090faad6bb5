'use client';

import ReceiptForm from '@/app/[locale]/orders/[name]/receipt/_components/receipt-form';
import type { ReceiptFormValues } from '@/app/[locale]/orders/[name]/receipt/_components/receipt-form';
import Receipt from '@/app/[locale]/orders/[name]/receipt/_components/receipt-pdf';
import { env } from '@/env';
import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';

import { AlertCircle, Printer } from 'lucide-react';
import { type ReactNode, useEffect, useRef, useState } from 'react';

interface ReceiptDialogProps {
  orderName: string;
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  trigger?: ReactNode;
  dictionary: Dictionary;
}

export default function ReceiptDialog({
  orderName,
  order,
  trigger,
  dictionary,
}: ReceiptDialogProps) {
  const [open, setOpen] = useState(false);
  const [showReceipt, setShowReceipt] = useState(false);
  const [formValues, setFormValues] = useState<ReceiptFormValues | null>(null);
  const receiptRef = useRef<HTMLDivElement>(null);
  const [isFulfilled, setIsFulfilled] = useState<boolean | null>(null);
  const [isCheckingFulfillment, setIsCheckingFulfillment] = useState(false);
  const [fulfillmentError, setFulfillmentError] = useState<string | null>(null);
  const [customizationId, setCustomizationId] = useState<string | null>(null);
  const [isCustomized, setIsCustomized] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Check if the order is fulfilled when the dialog opens
  useEffect(() => {
    if (open && isFulfilled === null && !isCheckingFulfillment) {
      checkFulfillmentStatus();
    }

    // Fetch existing receipt customization when dialog opens
    if (open && !customizationId) {
      fetchExistingCustomization();
    }
  }, [open, isFulfilled, isCheckingFulfillment, customizationId]);

  // Function to fetch existing receipt customization
  const fetchExistingCustomization = async () => {
    try {
      const response = await fetch('/api/get-receipt-customization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderName: orderName.replace('#', ''),
          email: order.email || '',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.customization?.id) {
          setCustomizationId(data.customization.id);
          setIsCustomized(data.customization.isCustomized || false);

          // If there are existing values, set them as form values
          if (
            !formValues &&
            (data.customization.recipientName || data.customization.note)
          ) {
            setFormValues({
              recipientName:
                data.customization.recipientName || defaultValues.recipientName,
              note: data.customization.note || defaultValues.note,
            });
          }
        }
      }
    } catch (error) {
      log.error('Error fetching receipt customization:', { error });
    }
  };

  // Function to check if the order is fulfilled
  const checkFulfillmentStatus = async () => {
    setIsCheckingFulfillment(true);
    setFulfillmentError(null);

    try {
      // Call the API to check fulfillment status
      const response = await fetch('/api/check-fulfillment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderName: orderName.replace('#', ''),
          email: order.email || '',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to check fulfillment status');
      }

      const data = (await response.json()) as {
        success: boolean;
        isFulfilled: boolean;
        message?: string;
      };

      if (!data.success) {
        setFulfillmentError(
          data.message || 'Failed to check fulfillment status'
        );

        // unfulfilled orders cannot download receipt
        return;
      }

      if (env.NEXT_PUBLIC_REGION === 'JP') {
        const response = await fetch('/api/check-logistic-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderName: order.name.replace('#', ''),
            email: order.email,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to check address change status');
        }

        const data = (await response.json()) as {
          logisticStatus: number;
        };

        if (data.logisticStatus < 4) {
          // status < 4 (waiting to be shipped) cannot download receipt
          return;
        }

        if (data.logisticStatus >= 7) {
          // status >= 7 (refunded) & 8 (cancelled) cannot download receipt
          return;
        }
      }

      setIsFulfilled(data.isFulfilled);
    } catch (error) {
      log.error('Error checking fulfillment status:', { error });
      setFulfillmentError(
        'An error occurred while checking fulfillment status'
      );
    } finally {
      setIsCheckingFulfillment(false);
    }
  };

  // Get default values from the order
  const defaultValues = {
    recipientName: order.billingAddress
      ? `${order.billingAddress.lastName || ''} ${order.billingAddress.firstName || ''}`.trim()
      : '',
    note: order.shippingAddress?.company || '',
  };

  const handleFormSubmit = async (values: ReceiptFormValues) => {
    // If already customized, prevent further edits
    if (isCustomized) {
      setShowReceipt(true);
      return;
    }

    // Show confirmation dialog for first-time customization
    if (!customizationId) {
      setFormValues(values);
      setShowConfirmation(true);
      return;
    }

    // Proceed with saving
    await saveCustomization(values);
  };

  const saveCustomization = async (values: ReceiptFormValues) => {
    setFormValues(values);

    // Save receipt customization to the database
    try {
      const response = await fetch('/api/save-receipt-customization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderName: orderName.replace('#', ''),
          email: order.email || '',
          recipientName: values.recipientName,
          note: values.note,
        }),
      });

      if (response.ok) {
        // Store the customization ID for later use
        const data = await response.json();
        if (data.success && data.customization?.id) {
          setCustomizationId(data.customization.id);
          setIsCustomized(true);
        }
      } else {
        const errorData = await response.json();
        alert(errorData.error || t.dialog.save_error);
        return;
      }
    } catch (error) {
      log.error('Error saving receipt customization:', { error });
      alert(t.dialog.save_error_generic);
      return;
    }

    setShowReceipt(true);
  };

  const handleConfirmSave = async () => {
    setShowConfirmation(false);
    if (formValues) {
      await saveCustomization(formValues);
    }
  };

  const handleCancelSave = () => {
    setShowConfirmation(false);
    setFormValues(null);
  };

  const handlePrint = async () => {
    if (!receiptRef.current) {
      return;
    }

    const element = receiptRef.current;
    const filename = `receipt-order-${orderName}.pdf`;

    // Configure html2pdf options
    const options = {
      margin: 0,
      filename: filename,
      image: { type: 'jpeg', quality: 1.0 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        letterRendering: true,
        logging: false,
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
        compress: true,
        putOnlyUsedFonts: true,
        pagesplit: false, // Prevent automatic page splits
      },
    };

    try {
      // Dynamically import html2pdf.js only on the client side
      const html2pdfModule = await import('html2pdf.js');
      const html2pdf = html2pdfModule.default;

      // Generate and open PDF
      html2pdf()
        .set(options)
        .from(element)
        .toContainer()
        .toCanvas()
        .toPdf()
        .get('pdf')
        .then((pdf: { output(type: string): Blob }) => {
          // Open PDF in new window
          const blob = pdf.output('blob');
          const blobUrl = URL.createObjectURL(blob);
          window.open(blobUrl, '_blank');

          // Update the download count if we have a customization ID
          if (customizationId) {
            updateDownloadCount(customizationId);
          }
        });
    } catch (error) {
      log.error('Error generating PDF:', { error });
      alert(t.dialog.pdf_error);
    }
  };

  // Function to update the download count
  const updateDownloadCount = async (id: string) => {
    try {
      const response = await fetch('/api/update-receipt-download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      if (!response.ok) {
        log.error('Failed to update receipt download count');
      }
    } catch (error) {
      log.error('Error updating receipt download count:', { error });
    }
  };

  const handleBack = () => {
    setShowReceipt(false);
  };

  // Default text if dictionary is not loaded yet
  const t = dictionary?.receipt || {
    dialog: {
      title: 'Generate Receipt',
      description: 'Customize the recipient details for your receipt.',
      back: 'Back',
      order_title: 'Receipt for Order {orderName}',
      view_pdf: 'View PDF',
      error_message:
        'There was an error downloading the PDF. Please try again.',
      checking_fulfillment: 'Checking order status...',
      not_fulfilled:
        'This order has not been fulfilled yet. You can download the receipt after the order has been fulfilled.',
      close: 'Close',
      confirm_title: 'Confirm Receipt Customization',
      confirm_description:
        'Are you sure you want to save these changes? You will not be able to edit the receipt again after this.',
      confirm_warning:
        'This action cannot be undone. Once you save these customizations, you will not be able to modify the receipt details again.',
      cancel: 'Cancel',
      confirm_save: 'Confirm & Save',
      already_customized:
        'This receipt has already been customized and cannot be edited again.',
      save_error: 'Failed to save receipt customization',
      save_error_generic: 'An error occurred while saving the customization',
      pdf_error: 'There was an error generating the PDF. Please try again.',
    },
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="cursor-pointer">{t.dialog.title}</Button>
        )}
      </DialogTrigger>
      <DialogContent
        className={
          showReceipt
            ? 'flex h-[90vh] min-w-[80vw] max-w-[95vw] flex-col overflow-hidden p-0'
            : 'max-w-md'
        }
      >
        {renderDialogContent()}
      </DialogContent>
    </Dialog>
  );

  // Helper function to render the appropriate content based on state
  function renderDialogContent() {
    if (isCheckingFulfillment) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <DialogTitle className="font-medium text-lg">
            {t.dialog.checking_fulfillment}
          </DialogTitle>
        </div>
      );
    }

    if (isFulfilled === false) {
      return (
        <div className="pt-6">
          <DialogTitle />
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{t.dialog.not_fulfilled}</AlertDescription>
          </Alert>
          <div className="flex justify-end">
            <Button variant="outline" onClick={() => setOpen(false)}>
              {t.dialog.close}
            </Button>
          </div>
        </div>
      );
    }

    if (showReceipt) {
      return (
        <div className="flex h-full flex-col print:p-0">
          {/* Fixed header with controls */}
          <div className="sticky top-0 z-10 grid cursor-pointer grid-cols-[140px_1fr_auto] items-center border-b bg-white p-4 print:hidden">
            <Button
              className="cursor-pointer"
              variant="outline"
              onClick={handleBack}
            >
              {t.dialog.back}
            </Button>
            <div className="flex-1 text-center font-medium">
              {t.dialog.order_title.replace('{orderName}', orderName)}
            </div>
            <div className="flex gap-2">
              <Button onClick={handlePrint} className="flex items-center gap-2">
                <Printer className="h-4 w-4" />
                {t.dialog.view_pdf}
              </Button>
            </div>
          </div>

          {/* Scrollable receipt container */}
          <div className="flex-1 overflow-auto p-4 print:p-0">
            <div className="receipt-container mx-auto flex justify-center print:m-0">
              <div
                ref={receiptRef}
                style={{
                  pageBreakInside: 'avoid',
                  pageBreakAfter: 'avoid',
                  pageBreakBefore: 'avoid',
                }}
              >
                <Receipt
                  orderName={orderName}
                  order={order}
                  customRecipientName={formValues?.recipientName}
                  note={formValues?.note}
                  dictionary={dictionary}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Confirmation dialog
    if (showConfirmation) {
      return (
        <>
          <DialogHeader>
            <DialogTitle>{t.dialog.confirm_title}</DialogTitle>
            <DialogDescription>
              {t.dialog.confirm_description}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{t.dialog.confirm_warning}</AlertDescription>
            </Alert>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCancelSave}>
                {t.dialog.cancel}
              </Button>
              <Button onClick={handleConfirmSave}>
                {t.dialog.confirm_save}
              </Button>
            </div>
          </div>
        </>
      );
    }

    // Default case - Form view
    return (
      <>
        <DialogHeader>
          <DialogTitle>{t.dialog.title}</DialogTitle>
          <DialogDescription>{t.dialog.description}</DialogDescription>
        </DialogHeader>
        {fulfillmentError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{fulfillmentError}</AlertDescription>
          </Alert>
        )}
        {isCustomized && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{t.dialog.already_customized}</AlertDescription>
          </Alert>
        )}
        <ReceiptForm
          defaultValues={formValues ?? defaultValues}
          onSubmit={handleFormSubmit}
          dictionary={dictionary}
          disabled={isCustomized}
        />
      </>
    );
  }
}
