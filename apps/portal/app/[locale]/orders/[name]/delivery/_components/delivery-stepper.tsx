import type { GetOrdersQuery } from '@/types/admin.generated';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { useEffect, useState } from 'react';

export function DeliveryStepper({
  order,
  dictionary,
}: {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}) {
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // get casefinite order status
  useEffect(() => {
    const checkLogisticStatus = async () => {
      setIsCheckingStatus(true);

      try {
        const response = await fetch('/api/check-logistic-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderName: order.name.replace('#', ''),
            email: order.email,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to check address change status');
        }

        const data = (await response.json()) as {
          logisticStatus: number;
        };

        let step = 1;
        if (data.logisticStatus >= 4) {
          step = 2;
        }
        if (data.logisticStatus >= 5) {
          step = 3;
        }
        if (data.logisticStatus >= 7) {
          step = 4;
        }

        setCurrentStep(step);
      } catch (error) {
        log.error('Error checking logistic status:', { error });
      } finally {
        setIsCheckingStatus(false);
      }
    };

    checkLogisticStatus();
  }, [order]);

  if (currentStep === 0 || isCheckingStatus) {
    return (
      <div className="flex w-full justify-between space-x-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  if (currentStep === 4) {
    return null;
  }

  return (
    <Stepper defaultValue={currentStep} orientation="horizontal">
      <StepperItem step={1} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">1</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 1 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery?.processing}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={2} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">2</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 2 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery?.preparing_for_delivery}
        </StepperTitle>
      </StepperItem>
      <StepperItem step={3} className="relative flex-1 flex-col! space-y-2">
        <StepperTrigger className="w-full flex-col items-start gap-2" asChild>
          <StepperIndicator
            asChild
            className="h-2 w-full rounded-none bg-border"
          >
            <span className="sr-only">3</span>
          </StepperIndicator>
        </StepperTrigger>
        <StepperTitle
          className={cn(
            'text-center font-medium text-muted-foreground/60 text-sm',
            currentStep === 3 && 'font-bold text-primary'
          )}
        >
          {dictionary.delivery?.delivered}
        </StepperTitle>
      </StepperItem>
    </Stepper>
  );
}
