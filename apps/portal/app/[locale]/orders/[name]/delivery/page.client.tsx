'use client';

import { CmsContent } from '@/components/cms-content';
import { env } from '@/env';
import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { dateFormatter as format } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Package,
  Truck,
} from 'lucide-react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { DeliveryStepper } from './_components/delivery-stepper';

interface DeliveryPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

// Helper function to format date
const formatDate = (
  dateString: string | null | undefined,
  notAvailable = 'N/A',
  invalidDate = 'Invalid date'
): string => {
  if (!dateString) {
    return notAvailable;
  }
  try {
    return format(new Date(dateString), 'yyyy-MM-dd');
  } catch (e) {
    log.error('Error formatting date:', { e });
    return invalidDate;
  }
};

export default function DeliveryPageClient({
  order,
  dictionary,
}: DeliveryPageClientProps) {
  const router = useRouter();
  const { locale } = useParams();

  const handleBack = () => {
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  // Analyze all fulfillments
  const fulfillments = order.fulfillments || [];
  const sortedFulfillments = [...fulfillments].sort((a, b) => {
    const dateA = a.updatedAt || a.createdAt;
    const dateB = b.updatedAt || b.createdAt;
    return new Date(dateB).getTime() - new Date(dateA).getTime();
  });

  // Determine overall delivery status based on Shopify's displayFulfillmentStatus
  const getOverallDeliveryStatus = () => {
    const shopifyStatus = order.displayFulfillmentStatus;

    switch (shopifyStatus) {
      case 'FULFILLED':
        return 'delivered';
      case 'PARTIALLY_FULFILLED':
        return 'partially_fulfilled';
      case 'IN_PROGRESS': {
        // Check if any fulfillments are in transit
        const hasInTransit = fulfillments.some(
          (f) => f.inTransitAt || f.status.toLowerCase().includes('transit')
        );
        return hasInTransit ? 'in_transit' : 'processing';
      }
      case 'ON_HOLD':
      case 'SCHEDULED':
        return 'processing';
      case 'REQUEST_DECLINED':
        return 'failed';
      case 'UNFULFILLED':
      case 'OPEN':
      case 'RESTOCKED':
        return 'pending';
      default:
        return 'pending';
    }
  };

  const overallStatus = getOverallDeliveryStatus();
  const hasMultipleFulfillments = fulfillments.length > 1;
  const latestFulfillment = sortedFulfillments[0] || null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'partially_fulfilled':
        return <Package className="h-6 w-6 text-orange-500" />;
      case 'in_transit':
        return <Truck className="h-6 w-6 text-blue-500" />;
      case 'attempted':
        return <AlertCircle className="h-6 w-6 text-yellow-500" />;
      case 'failed':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      default:
        return <Package className="h-6 w-6 text-muted-foreground" />;
    }
  };

  // Get status display text
  const getStatusDisplayText = (status: string): string => {
    switch (status) {
      case 'delivered':
        return dictionary.delivery.delivered;
      case 'partially_fulfilled':
        return dictionary.delivery.partially_fulfilled;
      case 'in_transit':
        return dictionary.delivery.in_transit;
      case 'attempted':
        return dictionary.delivery.attempted;
      case 'failed':
        return dictionary.delivery.failed;
      case 'processing':
        return dictionary.delivery.processing;
      case 'cancelled':
        return dictionary.delivery.cancelled;
      default:
        return dictionary.delivery.pending;
    }
  };

  // Use overall status for display
  const deliveryStatus = overallStatus;

  // Get additional status context
  const getStatusContext = () => {
    switch (order.displayFulfillmentStatus) {
      case 'PARTIALLY_FULFILLED':
        return dictionary.delivery.some_items_delivered;
      case 'IN_PROGRESS':
        return dictionary.delivery.processing;
      case 'ON_HOLD':
        return 'Order is on hold';
      case 'SCHEDULED':
        return 'Fulfillment is scheduled';
      case 'REQUEST_DECLINED':
        return 'Fulfillment request was declined';
      default:
        return null;
    }
  };

  // Format estimated delivery date
  const estimatedDelivery = latestFulfillment?.estimatedDeliveryAt
    ? formatDate(
        latestFulfillment.estimatedDeliveryAt,
        dictionary.delivery.not_available,
        dictionary.delivery.invalid_date
      )
    : dictionary.delivery.not_available;

  return (
    <div className="mx-auto max-w-2xl space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.return.delivery_confirmation}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {env.NEXT_PUBLIC_REGION === 'JP' && (
            <DeliveryStepper order={order} dictionary={dictionary} />
          )}

          {/* Optional CMS note */}
          <CmsContent
            contentKey="delivery_note"
            locale={locale as string}
            fallback=""
          />

          {fulfillments.length === 0 && (
            <Alert>
              <AlertDescription>
                {dictionary.delivery.no_information}
              </AlertDescription>
            </Alert>
          )}

          {/* Overall Status Summary */}
          <div className="flex items-center gap-4 rounded-lg bg-muted p-4">
            {getStatusIcon(deliveryStatus)}
            <div className="flex-1">
              <p className="font-medium">
                {getStatusDisplayText(deliveryStatus)}
              </p>
              {getStatusContext() && (
                <p className="text-muted-foreground text-sm">
                  {getStatusContext()}
                </p>
              )}
              {hasMultipleFulfillments && (
                <p className="text-muted-foreground text-sm">
                  {dictionary.delivery.multiple_shipments}
                </p>
              )}
              <p className="text-muted-foreground text-sm">
                {dictionary.delivery.estimated}: {estimatedDelivery}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {sortedFulfillments.map((fulfillment) => {
        const fulfillmentTrackingInfo =
          fulfillment.trackingInfo && fulfillment.trackingInfo.length > 0
            ? fulfillment.trackingInfo[0]
            : null;

        return (
          <Card key={fulfillment.id}>
            <CardContent>
              {fulfillmentTrackingInfo && (
                <div className="flex items-end justify-between">
                  <div>
                    <h3 className="font-medium">
                      {dictionary.delivery.tracking}
                    </h3>
                    <p className="text-sm">
                      {dictionary.delivery.carrier}:{' '}
                      {fulfillmentTrackingInfo.company ||
                        dictionary.delivery.not_available}
                    </p>
                    <p className="text-sm">
                      {dictionary.delivery.tracking_number}:{' '}
                      {fulfillmentTrackingInfo.number ||
                        dictionary.delivery.not_available}
                    </p>
                    <p className="text-sm">
                      {dictionary.delivery.created_on}:{' '}
                      {formatDate(fulfillment.createdAt)}
                    </p>
                  </div>
                  <Button size="sm" asChild>
                    <a
                      href={fulfillmentTrackingInfo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1"
                    >
                      <Truck className="h-3 w-3" />
                      {dictionary.delivery.track_package}
                    </a>
                  </Button>
                </div>
              )}

              <div className="mt-4 flex flex-col items-start gap-4 rounded-lg bg-muted p-4">
                {fulfillment.fulfillmentLineItems.edges.map((node) => {
                  return (
                    <div key={node.node.id} className="flex items-center gap-2">
                      <div className="flex-shrink-0">
                        <div className="relative h-10 w-10 overflow-hidden rounded">
                          {node.node.lineItem.image && (
                            <Image
                              src={node.node.lineItem.image.url}
                              alt={node.node.lineItem.name}
                              fill
                              style={{ objectFit: 'cover' }}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-sm">
                          {node.node.lineItem.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {dictionary.order.quantity}:{' '}
                          {node.node.lineItem.quantity}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );
      })}

      <div className="flex justify-between space-x-4 text-center">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          {dictionary.button.back}
        </Button>
      </div>
    </div>
  );
}
