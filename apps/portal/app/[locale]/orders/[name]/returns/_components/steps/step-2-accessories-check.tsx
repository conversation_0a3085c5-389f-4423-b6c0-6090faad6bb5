'use client';

import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { CustomRadio } from '@repo/design-system/components/ui/custom-radio';
import { AlertCircle } from 'lucide-react';
import SelectedItemsDisplay from '../selected-items-display';
import type { StepProps } from '../types';

export default function Step2AccessoriesCheck({
  flowState,
  updateFlowState,
  selectedItems,
  dictionary,
}: StepProps) {
  // Handle accessories check
  const handleAccessoriesCheck = (hasAccessories: boolean) => {
    updateFlowState({ hasAccessories });
  };

  return (
    <div className="space-y-4">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      <div>
        <h3 className="font-medium text-lg">
          {flowState.returnReason &&
            dictionary.return.reason[flowState.returnReason]}
        </h3>
        {(flowState.returnReason === 'wrong_size_model' ||
          flowState.returnReason === 'different_description') && (
          <p className="text-destructive-foreground text-sm">
            {dictionary.message.return_only}
          </p>
        )}
      </div>

      <h4>{dictionary.return.accessories.title}</h4>

      <div className="overflow-hidden rounded-md shadow-xs">
        {[
          { value: 'yes', label: dictionary.return.accessories.yes },
          { value: 'no', label: dictionary.return.accessories.no },
        ].map((item) => (
          <CustomRadio
            key={`accessories-${item.value}`}
            id={`accessories-${item.value}`}
            label={item.label}
            checked={
              (flowState.hasAccessories === true && item.value === 'yes') ||
              (flowState.hasAccessories === false && item.value === 'no')
            }
            onCheckedChange={() => handleAccessoriesCheck(item.value === 'yes')}
            name="hasAccessories"
            value={item.value}
          />
        ))}
      </div>

      {flowState.hasAccessories === false && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dictionary.message.no_submission}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
