'use client';

import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { CustomRadio } from '@repo/design-system/components/ui/custom-radio';
import { formatCurrency } from '@repo/design-system/lib/format';
import { AlertCircle, Calendar } from 'lucide-react';
import { calculateRefundFee } from '../return-flow-utils';
import SelectedItemsDisplay from '../selected-items-display';
import type { ReturnReason, StepProps } from '../types';

export default function Step1ReasonSelection({
  flowState,
  updateFlowState,
  selectedItems,
  dictionary,
  totalAmount,
}: StepProps) {
  // Handle reason selection
  const handleReasonSelect = (reason: ReturnReason) => {
    // Calculate refund fee based on reason
    const refundFee = calculateRefundFee(reason, totalAmount);

    updateFlowState({
      returnReason: reason,
      refundFee: refundFee,
    });
  };

  return (
    <div className="space-y-4">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      {/* Show return period messages */}
      {flowState.isExpired && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dictionary.return.expired_message}
          </AlertDescription>
        </Alert>
      )}

      {flowState.isSpecialApproval && (
        <Alert className="mb-4 border-amber-200 bg-amber-50">
          <AlertCircle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            {dictionary.return.special_approval_message}
          </AlertDescription>
        </Alert>
      )}

      {/* Show message for already processed items */}
      {flowState.lineItemsProcessed &&
        flowState.lineItemsProcessed.length > 0 && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {dictionary.return.already_processed}
            </AlertDescription>
          </Alert>
        )}

      <h3 className="font-medium text-lg">{dictionary.return.reason.title}</h3>

      <div className="overflow-hidden rounded-md shadow-xs">
        {[
          {
            value: 'wrong_size_model',
            label: dictionary.return.reason.wrong_size_model,
          },
          { value: 'defect', label: dictionary.return.reason.defect },
          {
            value: 'different_description',
            label: dictionary.return.reason.different_description,
          },
          {
            value: 'wrong_product',
            label: dictionary.return.reason.wrong_product,
          },
          {
            value: 'different_image',
            label: dictionary.return.reason.different_image,
          },
          {
            value: 'no_longer_needed',
            label: dictionary.return.reason.no_longer_needed,
          },
          {
            value: 'customer_damage',
            label: dictionary.return.reason.customer_damage,
          },
        ].map((item) => (
          <CustomRadio
            key={`reason-${item.value}`}
            id={`reason-${item.value}`}
            label={item.label}
            checked={flowState.returnReason === item.value}
            onCheckedChange={() =>
              handleReasonSelect(item.value as ReturnReason)
            }
            name="returnReason"
            value={item.value}
            disabled={
              flowState.isExpired ||
              flowState.lineItemsProcessed?.includes(selectedItems[0].node.id)
            }
          />
        ))}
      </div>

      {/* Show penalty fee for wrong size/model */}
      {flowState.returnReason === 'wrong_size_model' &&
        flowState.refundFee &&
        flowState.refundFee > 0 && (
          <Alert className="mt-4 border-amber-200 bg-amber-50">
            <AlertCircle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              {dictionary.return.refund_fee_message?.replace(
                '{fee}',
                formatCurrency(
                  flowState.refundFee.toString(),
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode === 'JPY'
                    ? 'ja-JP'
                    : 'en-US',
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode
                )
              )}
            </AlertDescription>
          </Alert>
        )}

      {/* Show order date information */}
      {flowState.orderDate && (
        <div className="mt-4 text-muted-foreground text-sm">
          <p className="flex items-center">
            <Calendar className="mr-2 h-4 w-4" />
            {flowState.hasDeliveryDate
              ? dictionary.return?.delivery_date
              : dictionary.return?.order_date}
            {flowState.orderDate.toLocaleDateString()}
          </p>
          {flowState.hasDeliveryDate && (
            <p className="mt-1 ml-6 text-xs">
              {dictionary.return.return_period}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
