'use client';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import { CustomRadio } from '@repo/design-system/components/ui/custom-radio';
import SelectedItemsDisplay from '../selected-items-display';
import type { StepProps } from '../types';

export default function Step4ReturnInstructions({
  orderName,
  flowState,
  updateFlowState,
  selectedItems,
  dictionary,
}: StepProps) {
  return (
    <div className="space-y-4">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      <div className="space-y-6">
        {/* Only show exchange instructions if the user chose exchange */}
        {flowState.exchangeType === 'exchange' && (
          <div>
            <h3 className="mb-3 font-medium text-lg">
              {dictionary.return.how_to_exchange?.title || 'How to Exchange'}
            </h3>
            <Card className="overflow-hidden">
              <CardContent className="space-y-3 px-4">
                <p className="text-sm">
                  {dictionary.return.how_to_exchange?.description ||
                    'We will send you the exchange item and a return envelope. Please return your item using the following method.'}
                </p>
                <p className="mt-2 font-medium text-sm">
                  {dictionary.return.how_to_exchange?.about_return ||
                    'About Return'}
                </p>
                <p className="text-sm">
                  {dictionary.return.how_to_exchange?.return_instructions ||
                    'After you receive and confirm the exchange product, please return your current item. Place it in the enclosed return envelope and drop it in a postal mailbox.'}
                </p>
                <div className="mt-2 rounded-lg border border-amber-100 bg-amber-50 p-3">
                  <p className="font-medium text-amber-800 text-sm">
                    {dictionary.return.how_to_exchange?.important_notice ||
                      'Important Notice'}
                  </p>
                  <p className="mt-1 text-amber-700 text-sm">
                    {dictionary.return.how_to_exchange?.return_period ||
                      'Due to an increase in unreturned items, we have established a return period. The return period is within 2 weeks after receiving the exchange product. If we do not receive your return within this period, we will contact you. Thank you for your understanding.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Only show return instructions if the user chose return or no exchange type is selected */}
        {(!flowState.exchangeType || flowState.exchangeType === 'return') && (
          <div>
            <h3 className="mb-3 font-medium text-lg">
              {dictionary.return.how_to_return?.title || 'How to Return'}
            </h3>
            <Card className="overflow-hidden">
              <CardContent className="space-y-4 px-4">
                <div>
                  <h4 className="mb-2 font-medium">
                    {dictionary.return.label_options || 'Return Label Options'}
                  </h4>
                  <div className="overflow-hidden rounded-md shadow-xs">
                    {[
                      {
                        value: 'print',
                        label:
                          dictionary.return.print_label_yourself ||
                          'Print the return label yourself',
                      },
                      {
                        value: 'envelope',
                        label:
                          dictionary.return.request_envelope ||
                          'Request a return envelope from us',
                      },
                    ].map((item) => (
                      <CustomRadio
                        key={`return-label-${item.value}`}
                        id={`return-label-${item.value}`}
                        label={item.label}
                        checked={flowState.returnLabelOption === item.value}
                        onCheckedChange={() =>
                          updateFlowState({
                            returnLabelOption: item.value as
                              | 'print'
                              | 'envelope',
                          })
                        }
                        name="returnLabelOption"
                        value={item.value}
                      />
                    ))}
                  </div>
                </div>

                {flowState.returnLabelOption === 'print' && (
                  <div className="mt-4 border-t pt-4">
                    <p className="text-sm">
                      {dictionary.return.print_label ||
                        'You will be able to download the return label after completing your request.'}
                    </p>
                  </div>
                )}

                {flowState.returnLabelOption === 'envelope' && (
                  <div className="mt-4 border-t pt-4">
                    <p className="mb-2 text-sm">
                      {dictionary.return.envelope_message ||
                        "We'll send you a return envelope to the address on your order. Please allow 3-5 business days for delivery."}
                    </p>
                  </div>
                )}

                <div className="border-t pt-4">
                  <h4 className="mb-2 font-medium">
                    {dictionary.return.return_instructions ||
                      'Return Instructions'}
                  </h4>
                  <ul className="list-disc space-y-3 pl-5">
                    <li className="text-sm">
                      {dictionary.return.how_to_return?.instructions[0] ||
                        'Please place the product along with all included items such as manuals and cables in the original packaging. Please note that returns cannot be accepted if any included items are missing.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.how_to_return?.instructions[1].replace(
                        '{returnNumber}',
                        `${orderName}`
                      ) ||
                        `Please write the return number ${orderName} on the outside of the box.`}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.how_to_return?.instructions[2] ||
                        'The product return label includes the return address and recipient information, so please print it and attach it to the envelope.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.how_to_return?.instructions[3] ||
                        'Please drop it off at your nearest mailbox within 7 business days.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.how_to_return?.instructions[4] ||
                        'Once we receive the product, we will process your refund.'}
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Only show refund information if the user chose return */}
        {(!flowState.exchangeType || flowState.exchangeType === 'return') && (
          <div>
            <h3 className="mb-3 font-medium text-lg">
              {dictionary.return.refund?.title || 'About Refund'}
            </h3>
            <Card className="overflow-hidden">
              <CardContent className="space-y-4 px-4">
                <p className="font-medium text-sm">
                  {dictionary.return.refund?.credit_card?.title ||
                    'Credit Card'}
                </p>
                <div>
                  <ul className="mt-2 list-disc space-y-2 pl-5">
                    <li className="text-sm">
                      {dictionary.return.refund?.credit_card?.instructions[0] ||
                        'Refunds will be processed according to the procedures set by your credit card company. Due to the billing cycle of your card company, the purchase amount may be charged once, and the refund may be processed in the following month or later.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.refund?.credit_card?.instructions[1] ||
                        'Refund methods and dates vary depending on your credit card company. For details about your refund, please contact your credit card company directly.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.refund?.credit_card?.instructions[2] ||
                        '(Due to privacy protection policies, we cannot obtain detailed information on your behalf.)'}
                    </li>
                  </ul>
                </div>
                <div className="border-t pt-4">
                  <p className="font-medium text-sm">
                    {dictionary.return.refund?.other_payment?.title ||
                      'auペイ、メルカリペイ、PayPay'}
                  </p>
                  <ul className="mt-2 list-disc space-y-2 pl-5">
                    <li className="text-sm">
                      {dictionary.return.refund?.other_payment
                        ?.instructions[0] ||
                        'We will cancel the payment for the returned product through each payment method. Due to the billing cycle of your card company, the purchase amount may be charged once, and the refund may be processed in the following month or later.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.refund?.other_payment
                        ?.instructions[1] ||
                        'Refund methods and dates vary depending on your card company. For details about your refund, please contact each payment service provider and your card company directly.'}
                    </li>
                    <li className="text-sm">
                      {dictionary.return.refund?.other_payment
                        ?.instructions[2] ||
                        '(Due to privacy protection policies, we cannot obtain detailed information on your behalf.)'}
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
