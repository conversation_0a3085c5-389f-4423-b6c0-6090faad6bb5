'use client';

import { ReturnLabelGenerator } from '@/components/return-label-generator';
import type { GetOrdersQuery } from '@/types/admin.generated';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatCurrency } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import { Check } from 'lucide-react';
import Image from 'next/image';
import type { FlowState } from '../types';

interface CompletedStateProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  dictionary: Dictionary;
  flowState: FlowState;
  onComplete: () => void;
  orderName?: string;
}

export default function CompletedState({
  selectedItems,
  dictionary,
  flowState,
  onComplete,
  orderName,
}: CompletedStateProps) {
  return (
    <Card className="mx-auto w-full max-w-3xl">
      <CardHeader>
        <CardTitle className="flex items-center text-green-600">
          <Check className="mr-2 h-6 w-6" />{' '}
          {dictionary.return.request_submitted || 'Return Request Submitted'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg border border-green-100 bg-green-50 p-4 text-center">
          <p className="font-medium text-green-800 text-lg">
            {dictionary.return.thank_you ||
              'Thank you for your return request!'}
          </p>
          <p className="mt-1 text-green-700 text-sm">
            {dictionary.return.request_success ||
              'Your return request has been submitted successfully.'}
          </p>
          <p className="mt-3 font-medium text-green-800 text-sm">
            {dictionary.return.return_number || 'Return Number:'}{' '}
            {flowState.returnNumber}
          </p>
        </div>

        <div className="rounded-md border bg-gray-50 p-4">
          <h4 className="mb-3 font-medium text-muted-foreground-700 text-sm">
            {dictionary.return.items_returned || 'Items Being Returned'}
          </h4>
          <div className="space-y-3">
            {selectedItems.map((item) => (
              <div key={item.node.id} className="flex items-center">
                <div className="mr-3 flex-shrink-0">
                  <div className="relative h-12 w-12 overflow-hidden rounded">
                    {item.node.image && (
                      <Image
                        src={item.node.image.url}
                        alt={item.node.title}
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    )}
                  </div>
                </div>
                <div className="flex-grow">
                  <p className="font-medium text-sm">{item.node.title}</p>
                  <p className="text-muted-foreground text-xs">
                    {item.node.variantTitle}
                  </p>
                </div>
                <div className="flex-shrink-0 text-right">
                  <p className="font-medium text-sm">
                    {formatCurrency(
                      item.node.originalUnitPriceSet.shopMoney.amount,
                      item.node.originalUnitPriceSet.shopMoney.currencyCode ===
                        'JPY'
                        ? 'ja-JP'
                        : 'en-US',
                      item.node.originalUnitPriceSet.shopMoney.currencyCode
                    )}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {dictionary.order.quantity}: {item.node.quantity}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium">
            {dictionary.return.next_steps || 'Next Steps:'}
          </h3>
          <ol className="list-inside list-decimal space-y-1 text-sm">
            <li>
              {dictionary.return.email_confirmation ||
                'You will receive a confirmation email shortly.'}
            </li>
            <li>
              {dictionary.return.package_securely ||
                'Please package your items securely with all original packaging and tags.'}
            </li>
            <li>
              {dictionary.return.print_label ||
                'Print the return label that will be included in the email.'}
            </li>
            <li>
              {dictionary.return.drop_off ||
                'Drop off the package at your nearest postal service location.'}
            </li>
            <li>
              {dictionary.return.refund_processed ||
                'Your refund will be processed once we receive and inspect the returned items.'}
            </li>
          </ol>
        </div>

        {/* Download Label Section - Only show if return label option is print */}
        {flowState.returnLabelOption === 'print' &&
          orderName &&
          flowState.returnNumber && (
            <div className="mt-4 rounded-lg border border-green-100 bg-green-50 p-4">
              <h3 className="mb-3 font-medium text-green-800">
                {dictionary.return.download_label || 'Download Return Label'}
              </h3>
              <p className="mb-3 text-green-700 text-sm">
                {dictionary.return.download_label_description ||
                  'Download and print your return label to attach to your package.'}
              </p>

              <ReturnLabelGenerator
                returnNumber={flowState.returnNumber}
                orderName={orderName}
                dictionary={dictionary}
              />
            </div>
          )}

        <div className="mt-4 rounded-lg border border-blue-100 bg-blue-50 p-4">
          <p className="text-blue-800 text-sm">
            {dictionary.return.questions_contact ||
              'If you have any questions about your return, please contact our customer service team and reference your return number.'}
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={onComplete} className="w-full">
          {dictionary.return.done || 'Done'}
        </Button>
      </CardFooter>
    </Card>
  );
}
