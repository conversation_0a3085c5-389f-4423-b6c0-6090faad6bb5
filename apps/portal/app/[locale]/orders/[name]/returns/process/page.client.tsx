'use client';

import ReturnFlowStepper from '@/app/[locale]/orders/[name]/returns/_components/return-flow-stepper';
import type { GetOrdersQuery } from '@/types/admin.generated';
import type { Dictionary } from '@repo/internationalization';
import { useRouter } from 'next/navigation';

export default function ProcessPageClient({
  selectedItems,
  order,
  dictionary,
  autoApprovalDays,
}: {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  autoApprovalDays: number;
}) {
  const router = useRouter();

  const handleComplete = () => {
    // Extract order name from the URL
    const pathParts = window.location.pathname.split('/');
    const orderName = pathParts[pathParts.indexOf('orders') + 1];

    // Navigate back to the service selection page with success message
    router.push(
      `/orders/${orderName}/services?email=${encodeURIComponent(
        window.location.search.includes('email=')
          ? decodeURIComponent(
              window.location.search.split('email=')[1].split('&')[0]
            )
          : ''
      )}&success=return_processed`
    );
  };

  const handleCancel = () => {
    // Extract order name from the URL
    const pathParts = window.location.pathname.split('/');
    const orderName = pathParts[pathParts.indexOf('orders') + 1];

    // Navigate back to the returns page
    router.push(
      `/orders/${orderName}/returns?email=${encodeURIComponent(
        window.location.search.includes('email=')
          ? decodeURIComponent(
              window.location.search.split('email=')[1].split('&')[0]
            )
          : ''
      )}`
    );
  };

  return (
    <ReturnFlowStepper
      selectedItems={selectedItems}
      order={order}
      dictionary={dictionary}
      onComplete={handleComplete}
      onCancel={handleCancel}
      autoApprovalDays={autoApprovalDays}
    />
  );
}
