import { prisma } from '@/lib/prisma';
import { getOrderByName } from '@/lib/shopify';
import { getDictionary } from '@repo/internationalization';
import { redirect } from 'next/navigation';
import ProcessPageClient from './page.client';

interface ProcessPageProps {
  params: Promise<{
    name: string;
    locale: string;
  }>;
  searchParams: Promise<{
    email?: string;
    items?: string;
  }>;
}

export default async function ProcessPage({
  params,
  searchParams,
}: ProcessPageProps) {
  const { name, locale } = await params;
  const { email, items } = await searchParams;

  const dictionary = await getDictionary(locale);

  const autoApprovalSettings = await prisma.appSettings.findUnique({
    where: { key: 'auto_approval_days' },
  });

  const autoApprovalDays = autoApprovalSettings
    ? Number.parseInt(autoApprovalSettings.value, 10)
    : 30; // Default to 30 days

  // Verify order access with email if provided
  const order = await getOrderByName(name, email);

  // If order not found or email verification failed
  if (!order) {
    // Redirect to home page with error message, including the locale
    redirect(
      `/${locale}?error=verification_failed&orderName=${encodeURIComponent(name)}`
    );
  }

  // Parse selected items from query params
  const selectedItemIds = items ? items.split(',') : [];

  // Filter the order items to only include the selected ones
  const selectedItems =
    order.lineItems?.edges.filter((item) =>
      selectedItemIds.includes(String(item.node.id))
    ) || [];

  // If no items were selected, redirect back to the returns page
  if (selectedItems.length === 0) {
    redirect(
      `/${locale}/orders/${name}/returns?email=${encodeURIComponent(email || '')}`
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-center font-bold text-3xl">
        {dictionary.order.return_exchange}
      </h1>
      <ProcessPageClient
        selectedItems={selectedItems}
        order={order}
        dictionary={dictionary}
        autoApprovalDays={autoApprovalDays}
      />
    </div>
  );
}
