'use client';

import type { GetOrdersQuery } from '@/types/admin.generated';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatCurrency } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface ReturnsPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

export default function ReturnsPageClient({
  order,
  dictionary,
}: ReturnsPageClientProps) {
  const router = useRouter();

  const handleItemSelect = (itemId: string) => {
    // Navigate to the return page with the selected item
    router.push(
      `/orders/${order.name.replace(
        '#',
        ''
      )}/returns/process?items=${itemId}&email=${encodeURIComponent(order.email || '')}`
    );
  };

  const handleBack = () => {
    // Navigate back to the service selection page
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  if (order.displayFulfillmentStatus !== 'FULFILLED') {
    return (
      <div className="mx-auto max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>{dictionary.return.return_request}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="rounded-lg bg-muted p-6 text-center">
              <p className="text-lg">{dictionary.return.return_not_allowed}</p>
            </div>

            <div className="mt-8 flex justify-between text-center">
              <Button
                variant="outline"
                onClick={handleBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {dictionary.button.back}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-2xl space-y-8">
      {/* Main heading and subheading */}
      <div className="space-y-2 text-center">
        <h2 className="font-semibold text-2xl">
          {dictionary.order.select_items}
        </h2>
      </div>

      {/* Item cards */}
      <div className="space-y-4">
        {order.lineItems?.edges.map((item) => (
          <Card
            key={item.node.id}
            className="cursor-pointer overflow-hidden shadow-xs transition-shadow hover:shadow-sm"
            onClick={() => handleItemSelect(String(item.node.id))}
          >
            <div className="flex items-center px-4">
              {/* Product image */}
              <div className="mr-4 flex-shrink-0">
                <div className="relative h-20 w-20 overflow-hidden rounded-lg">
                  {item.node.image && (
                    <Image
                      src={item.node.image.url}
                      alt={item.node.title}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  )}
                </div>
              </div>

              {/* Product details */}
              <div className="flex-grow">
                <p className="text-muted-foreground text-sm">
                  {item.node.variantTitle}
                </p>
                <h3 className="font-medium text-md">{item.node.title}</h3>
                <p className="mt-2 text-sm">
                  {formatCurrency(
                    item.node.originalUnitPriceSet?.shopMoney.amount,
                    item.node.originalUnitPriceSet.shopMoney.currencyCode ===
                      'JPY'
                      ? 'ja-JP'
                      : 'en-US',
                    item.node.originalUnitPriceSet.shopMoney.currencyCode
                  )}
                </p>
              </div>

              {/* Arrow icon */}
              <div className="ml-2 flex-shrink-0">
                <ArrowRight className="h-5 w-5 text-muted-foreground-400" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Back button */}
      <div className="mt-8 text-center">
        <Button
          variant="outline"
          onClick={handleBack}
          className="flex items-center gap-2 "
        >
          <ArrowLeft className="h-4 w-4" />
          {dictionary.button.back}
        </Button>
      </div>
    </div>
  );
}
