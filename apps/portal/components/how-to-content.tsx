'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import { useParams } from 'next/navigation';
import { CmsContent } from './cms-content';

interface HowToContentProps {
  contentType: 'exchange' | 'return' | 'refund';
  className?: string;
}

/**
 * Component to display "How To" content from CMS
 * Used for "How to Exchange", "How to Return", and "About Refund" sections
 */
export function HowToContent({
  contentType,
  className = '',
}: HowToContentProps) {
  const { locale } = useParams();

  // Get content key
  let contentKey = 'about_refund';
  if (contentType === 'exchange') {
    contentKey = 'how_to_exchange';
  } else if (contentType === 'return') {
    contentKey = 'how_to_return';
  }

  // Get translated title
  let title = 'About Refund';
  if (locale === 'ja') {
    if (contentType === 'exchange') {
      title = '交換方法';
    } else if (contentType === 'return') {
      title = '返品方法';
    } else {
      title = '返金について';
    }
  } else if (contentType === 'exchange') {
    title = 'How to Exchange';
  } else if (contentType === 'return') {
    title = 'How to Return';
  } else {
    title = 'About Refund';
  }

  // Simple translation for fallback message
  const fallbackMessage =
    locale === 'ja'
      ? `${title}に関する情報がありません。`
      : `No information available about ${title.toLowerCase()}.`;

  return (
    <Accordion type="single" collapsible className={className}>
      <AccordionItem value="content">
        <AccordionTrigger>{title}</AccordionTrigger>
        <AccordionContent>
          <CmsContent
            contentKey={contentKey}
            locale={locale as string}
            fallback={fallbackMessage}
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
