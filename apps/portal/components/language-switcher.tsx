'use client';

import { Button } from '@repo/design-system/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu';
import { Languages } from 'lucide-react';
import { useParams, usePathname, useRouter } from 'next/navigation';

const languages = [
  { label: '🇬🇧 English', value: 'en' },
  { label: '🇯🇵 日本語', value: 'ja' },
];

export const LanguageSwitcher = () => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  const switchLanguage = (locale: string) => {
    // Replace the current locale in the pathname with the new one
    let newPathname = pathname.replace(`/${params.locale}`, `/${locale}`);
    if (newPathname === '/') {
      newPathname = `/${locale}`;
    }

    router.push(newPathname);
  };

  // Simple translation for sr-only text
  const switchLanguageText =
    params.locale === 'ja' ? '言語を切り替え' : 'Switch language';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="shrink-0 text-foreground"
        >
          <Languages className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">{switchLanguageText}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {languages.map(({ label, value }) => (
          <DropdownMenuItem key={value} onClick={() => switchLanguage(value)}>
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
