'use client';

import { Button } from '@repo/design-system/components/ui/button';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { Download } from 'lucide-react';
import { useRef, useState } from 'react';

interface ReturnLabelGeneratorProps {
  orderName: string;
  returnNumber: string;
  customerName?: string;
  customerAddress?: string;
  dictionary: Dictionary;
  companyName?: string;
  companyAddress?: string;
  returnDepartment?: string;
}

export function ReturnLabelGenerator({
  orderName,
  returnNumber,
  customerName,
  customerAddress,
  dictionary,
  companyName = 'Casefinite JP', // Default company name
  companyAddress = 'Casefinite Address', // Default company address
  returnDepartment = '', // Default return department
}: ReturnLabelGeneratorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const generatePDF = async () => {
    setIsGenerating(true);

    try {
      // Import dynamically to avoid server-side rendering issues
      // Note: These packages need to be installed:
      // npm install jspdf jsbarcode --save
      const { jsPDF } = await import('jspdf');
      const JsBarcode = (await import('jsbarcode')).default;

      // Create a new PDF document
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // Set up the document
      doc.setFontSize(16);
      doc.text('Return Label', 105, 20, { align: 'center' });

      // Add order information
      doc.setFontSize(12);
      doc.text(`Order: ${orderName}`, 20, 40);

      if (customerName) {
        doc.text(`Customer: ${customerName}`, 20, 50);
      }

      if (customerAddress) {
        doc.text(`Address: ${customerAddress}`, 20, 60);
      }

      // Generate barcode if canvas ref exists
      if (canvasRef.current) {
        // Generate barcode on canvas
        JsBarcode(canvasRef.current, returnNumber, {
          format: 'CODE128',
          width: 2,
          height: 50,
          displayValue: true,
        });

        // Add the barcode to the PDF
        const barcodeDataUrl = canvasRef.current.toDataURL('image/png');
        doc.addImage(barcodeDataUrl, 'PNG', 50, 80, 100, 30);
      }

      // Add company address and return instructions
      doc.setFontSize(10);
      doc.text('Return to:', 20, 130);
      doc.text(companyName, 20, 140);
      doc.text(returnDepartment, 20, 145);
      doc.text(companyAddress, 20, 150);

      // Add instructions
      doc.setFontSize(11);
      doc.text('Instructions:', 20, 170);
      doc.text('1. Cut along the dotted line', 25, 180);
      doc.text('2. Attach this label to your package', 25, 185);
      doc.text('3. Drop off at your nearest postal service location', 25, 190);

      // Save the PDF
      doc.save(`return-label-${orderName.replace('#', '')}.pdf`);
    } catch (error) {
      log.error('Error generating PDF:', { error });
      alert('There was an error generating the PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Hidden canvas for barcode generation */}
      <canvas ref={canvasRef} className="hidden" />

      <Button
        onClick={generatePDF}
        className="flex items-center gap-2"
        disabled={isGenerating}
      >
        <Download className="h-4 w-4" />
        {isGenerating
          ? dictionary.return.generating_label || 'Generating Label...'
          : dictionary.return.download_label || 'Download Return Label'}
      </Button>
    </div>
  );
}
