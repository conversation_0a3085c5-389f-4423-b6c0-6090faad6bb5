/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
import type * as AdminTypes from './admin.types';

export type GetOrderBasicInfoQueryVariables = AdminTypes.Exact<{
  query: AdminTypes.Scalars['String']['input'];
}>;

export type GetOrderBasicInfoQuery = {
  orders: {
    edges: Array<{
      node: Pick<
        AdminTypes.Order,
        'id' | 'name' | 'email' | 'statusPageUrl'
      > & {
        shippingAddress?: AdminTypes.Maybe<
          Pick<
            AdminTypes.MailingAddress,
            | 'firstName'
            | 'lastName'
            | 'address1'
            | 'address2'
            | 'city'
            | 'province'
            | 'zip'
            | 'country'
            | 'phone'
            | 'company'
          >
        >;
      };
    }>;
  };
};

export type GetOrdersQueryVariables = AdminTypes.Exact<{
  query: AdminTypes.Scalars['String']['input'];
}>;

export type GetOrdersQuery = {
  orders: {
    edges: Array<{
      node: Pick<
        AdminTypes.Order,
        | 'id'
        | 'legacyResourceId'
        | 'name'
        | 'statusPageUrl'
        | 'createdAt'
        | 'processedAt'
        | 'email'
        | 'taxesIncluded'
        | 'paymentGatewayNames'
        | 'note'
        | 'displayFulfillmentStatus'
      > & {
        totalPriceSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        subtotalPriceSet?: AdminTypes.Maybe<{
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        }>;
        totalShippingPriceSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        totalTaxSet?: AdminTypes.Maybe<{
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        }>;
        totalDiscountsSet?: AdminTypes.Maybe<{
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        }>;
        currentTotalPriceSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        currentSubtotalPriceSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        currentTotalTaxSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        currentTotalDiscountsSet: {
          shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
        };
        shippingAddress?: AdminTypes.Maybe<
          Pick<
            AdminTypes.MailingAddress,
            | 'firstName'
            | 'lastName'
            | 'address1'
            | 'address2'
            | 'city'
            | 'province'
            | 'zip'
            | 'country'
            | 'phone'
            | 'company'
          >
        >;
        billingAddress?: AdminTypes.Maybe<
          Pick<
            AdminTypes.MailingAddress,
            | 'firstName'
            | 'lastName'
            | 'address1'
            | 'address2'
            | 'city'
            | 'province'
            | 'zip'
            | 'country'
            | 'phone'
            | 'company'
          >
        >;
        lineItems: {
          edges: Array<{
            node: Pick<
              AdminTypes.LineItem,
              'id' | 'name' | 'title' | 'variantTitle' | 'quantity' | 'sku'
            > & {
              variant: Pick<AdminTypes.ProductVariant, 'barcode'>;
              originalUnitPriceSet: {
                shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
              };
              discountedTotalSet: {
                shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
              };
              totalDiscountSet: {
                shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
              };
              image?: AdminTypes.Maybe<Pick<AdminTypes.Image, 'url'>>;
              taxLines: Array<
                Pick<AdminTypes.TaxLine, 'rate' | 'title'> & {
                  priceSet: {
                    shopMoney: Pick<
                      AdminTypes.MoneyV2,
                      'amount' | 'currencyCode'
                    >;
                  };
                }
              >;
            };
          }>;
        };
        taxLines: Array<
          Pick<AdminTypes.TaxLine, 'rate' | 'title'> & {
            priceSet: {
              shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
            };
          }
        >;
        fulfillments: Array<
          Pick<
            AdminTypes.Fulfillment,
            | 'id'
            | 'status'
            | 'createdAt'
            | 'updatedAt'
            | 'deliveredAt'
            | 'estimatedDeliveryAt'
            | 'inTransitAt'
          > & {
            trackingInfo: Pick<
              AdminTypes.FulfillmentTrackingInfo,
              'company' | 'number' | 'url'
            >[];
            events: {
              edges: Array<{
                node: Pick<
                  AdminTypes.FulfillmentEvent,
                  | 'id'
                  | 'status'
                  | 'happenedAt'
                  | 'city'
                  | 'province'
                  | 'country'
                  | 'message'
                >;
              }>;
            };
            fulfillmentLineItems: {
              edges: Array<{
                node: Pick<AdminTypes.FulfillmentLineItem, 'id'> & {
                  lineItem: Pick<
                    AdminTypes.LineItem,
                    'id' | 'name' | 'quantity'
                  > & {
                    image?: AdminTypes.Maybe<Pick<AdminTypes.Image, 'url'>>;
                  };
                };
              }>;
            };
          }
        >;
        shippingLine?: AdminTypes.Maybe<
          Pick<AdminTypes.ShippingLine, 'title'> & {
            originalPriceSet: {
              shopMoney: Pick<AdminTypes.MoneyV2, 'amount' | 'currencyCode'>;
            };
          }
        >;
        customAttributes: Pick<AdminTypes.Attribute, 'key' | 'value'>[];
      };
    }>;
  };
};

export type UpdateOrderShippingAddressMutationVariables = AdminTypes.Exact<{
  id: AdminTypes.Scalars['ID']['input'];
  address: AdminTypes.MailingAddressInput;
}>;

export type UpdateOrderShippingAddressMutation = {
  orderUpdate?: AdminTypes.Maybe<{
    order?: AdminTypes.Maybe<
      Pick<AdminTypes.Order, 'id'> & {
        shippingAddress?: AdminTypes.Maybe<
          Pick<
            AdminTypes.MailingAddress,
            | 'firstName'
            | 'lastName'
            | 'address1'
            | 'address2'
            | 'city'
            | 'province'
            | 'zip'
            | 'country'
            | 'phone'
            | 'company'
          >
        >;
      }
    >;
    userErrors: Pick<AdminTypes.UserError, 'field' | 'message'>[];
  }>;
};

interface GeneratedQueryTypes {
  '#graphql\n        query GetOrderBasicInfo($query: String!) {\n            orders(first: 1, query: $query) {\n                edges {\n                    node {\n                        id\n                        name\n                        email\n                        statusPageUrl\n                        shippingAddress {\n                            firstName\n                            lastName\n                            address1\n                            address2\n                            city\n                            province\n                            zip\n                            country\n                            phone\n                            company\n                        }\n                    }\n                }\n            }\n        }\n    ': {
    return: GetOrderBasicInfoQuery;
    variables: GetOrderBasicInfoQueryVariables;
  };
  '#graphql\n        query GetOrders($query: String!) {\n            orders(first: 1, query: $query) {\n                edges {\n                    node {\n                        id # This is the GID (e.g., gid://shopify/Order/...)\n                        legacyResourceId # This is the numeric ID you passed in\n                        name # This is the order number (e.g., #1001)\n                        statusPageUrl\n                        createdAt\n                        processedAt\n                        email\n                        taxesIncluded\n                        totalPriceSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        subtotalPriceSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        totalShippingPriceSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        totalTaxSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        totalDiscountsSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        currentTotalPriceSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        currentSubtotalPriceSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        currentTotalTaxSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        currentTotalDiscountsSet {\n                            shopMoney {\n                                amount\n                                currencyCode\n                            }\n                        }\n                        shippingAddress {\n                            firstName\n                            lastName\n                            address1\n                            address2\n                            city\n                            province\n                            zip\n                            country\n                            phone\n                            company\n                        }\n                        billingAddress {\n                            firstName\n                            lastName\n                            address1\n                            address2\n                            city\n                            province\n                            zip\n                            country\n                            phone\n                            company\n                        }\n                        lineItems(first: 25) {\n                            edges {\n                                node {\n                                    id\n                                    name # Combination of product title and variant title\n                                    title # Product title\n                                    variantTitle\n                                    quantity\n                                    sku\n                                    originalUnitPriceSet {\n                                        shopMoney {\n                                            amount\n                                            currencyCode\n                                        }\n                                    }\n                                    discountedTotalSet {\n                                        shopMoney {\n                                            amount\n                                            currencyCode\n                                        }\n                                    }\n                                    totalDiscountSet {\n                                        shopMoney {\n                                            amount\n                                            currencyCode\n                                        }\n                                    }\n                                    image {\n                                        url\n                                    }\n                                    taxLines {\n                                        priceSet {\n                                            shopMoney {\n                                                amount\n                                                currencyCode\n                                            }\n                                        }\n                                        rate\n                                        title\n                                    }\n                                }\n                            }\n                        }\n                        taxLines {\n                            priceSet {\n                                shopMoney {\n                                    amount\n                                    currencyCode\n                                }\n                            }\n                            rate\n                            title\n                        }\n                        paymentGatewayNames\n                        fulfillments(first: 10) {\n                            id\n                            status\n                            createdAt\n                            updatedAt\n                            deliveredAt\n                            estimatedDeliveryAt\n                            inTransitAt\n                            trackingInfo {\n                                company\n                                number\n                                url\n                            }\n                            events(first: 10) {\n                                edges {\n                                    node {\n                                        id\n                                        status\n                                        happenedAt\n                                        city\n                                        province\n                                        country\n                                        message\n                                    }\n                                }\n                            }\n                        }\n                        shippingLine {\n                            title\n                            originalPriceSet {\n                                shopMoney {\n                                    amount\n                                    currencyCode\n                                }\n                            }\n                        }\n                        note\n                        customAttributes {\n                            key\n                            value\n                        }\n                    }\n                }\n            }\n        }\n    ': {
    return: GetOrdersQuery;
    variables: GetOrdersQueryVariables;
  };
}

interface GeneratedMutationTypes {
  '#graphql\n        mutation UpdateOrderShippingAddress($id: ID!, $address: MailingAddressInput!) {\n          orderUpdate(input: {\n            id: $id,\n            shippingAddress: $address\n          }) {\n            order {\n              id\n              shippingAddress {\n                firstName\n                lastName\n                address1\n                address2\n                city\n                province\n                zip\n                country\n                phone\n                company\n              }\n            }\n            userErrors {\n              field\n              message\n            }\n          }\n        }\n    ': {
    return: UpdateOrderShippingAddressMutation;
    variables: UpdateOrderShippingAddressMutationVariables;
  };
}
declare module '@shopify/admin-api-client' {
  type InputMaybe<T> = AdminTypes.InputMaybe<T>;
  interface AdminQueries extends GeneratedQueryTypes {}
  interface AdminMutations extends GeneratedMutationTypes {}
}
