// Define interfaces for the order data structure
export interface MoneySet {
  shopMoney: {
    amount: string;
    currencyCode: string;
  };
}

export interface Address {
  firstName?: string;
  lastName?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  zip?: string;
  country?: string;
  phone?: string;
  company?: string;
}

export interface TaxLine {
  priceSet: MoneySet;
  rate: string;
  title: string;
}

export interface LineItem {
  id: string;
  name: string;
  title: string;
  variantTitle?: string;
  quantity: number;
  sku?: string;
  variant: {
    barcode?: string;
  };
  originalUnitPriceSet: MoneySet;
  discountedTotalSet?: MoneySet;
  totalDiscountSet?: MoneySet;
  image?: {
    url: string;
  };
  taxLines?: TaxLine[];
}

export interface ShopifyOrder {
  id: string;
  name: string;
  createdAt: string;
  processedAt: string;
  email?: string;
  taxesIncluded: boolean;
  totalPriceSet: MoneySet;
  subtotalPriceSet: MoneySet;
  totalShippingPriceSet: MoneySet;
  totalTaxSet: MoneySet;
  totalDiscountsSet?: MoneySet;
  currentTotalPriceSet: MoneySet;
  currentSubtotalPriceSet: MoneySet;
  currentTotalTaxSet: MoneySet;
  currentTotalDiscountsSet?: MoneySet;
  shippingAddress?: Address;
  billingAddress?: Address;
  lineItems: {
    edges: Array<{
      node: LineItem;
    }>;
  };
  taxLines?: TaxLine[];
  paymentGatewayNames?: string[];
  shippingLine?: {
    title: string;
    originalPriceSet: MoneySet;
  };
  note?: string;
  customAttributes?: Array<{
    key: string;
    value: string;
  }>;
}
