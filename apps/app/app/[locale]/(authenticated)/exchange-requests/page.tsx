import type { Metada<PERSON> } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getExchangeRequests } from '../return-requests/actions';
import { ReturnRequestsPageClient } from './page.client';

const title = 'Senders Return - Exchange Requests';
const description = 'Manage customer exchange requests';

export const metadata: Metadata = {
  title,
  description,
};

const ExchangeRequestsPage = async (): Promise<ReactElement> => {
  const exchangeRequests = await getExchangeRequests();

  return (
    <>
      <Header page="Exchange Requests" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReturnRequestsPageClient exchangeRequests={exchangeRequests} />
        </div>
      </div>
    </>
  );
};

export default ExchangeRequestsPage;
