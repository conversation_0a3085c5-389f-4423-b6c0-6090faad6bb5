import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getExchangeRequests } from '../return-requests/actions';
import { ExchangeRequestsPageClient } from './page.client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.navigation.exchange_requests}`,
    description: 'Manage customer exchange requests',
  };
}

const ExchangeRequestsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const exchangeRequests = await getExchangeRequests();

  return (
    <>
      <Header page={dictionary.admin.navigation.exchange_requests} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ExchangeRequestsPageClient {...{ dictionary, exchangeRequests }} />
        </div>
      </div>
    </>
  );
};

export default ExchangeRequestsPage;
