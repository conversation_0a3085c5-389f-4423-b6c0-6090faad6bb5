import { Header } from '@/app/(authenticated)/components/header';
import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import {
  <PERSON>bs,
  <PERSON>bsContent,
  <PERSON>bsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import type { Metadata } from 'next';
import { title } from 'radash';
import type { ReactElement } from 'react';
import { BarcodeScannerTab } from '../../return-requests/[id]/barcode-scanner-tab';
import { ReturnRequestImageDialog } from '../../return-requests/[id]/page.client';
import { ReturnRequestStatusForm } from '../../return-requests/[id]/return-request-status-form';
import { getReturnRequest } from '../../return-requests/actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const { returnRequest } = await getReturnRequest(id);

  const title = 'Senders Return - Exchange Request';
  const description = `Details for Exchange ${returnRequest.returnNumber}`;

  return {
    title,
    description,
  };
}

export default async function ExchangeRequestDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const { returnRequest, isBlacklisted, returnCount } =
    await getReturnRequest(id);

  return (
    <>
      <Header pages={['Exchange Requests']} page={returnRequest.returnNumber} />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            Return {returnRequest.returnNumber}
          </h2>
          <ReturnRequestStatusForm
            id={returnRequest.id}
            currentStatus={returnRequest.status}
            processed={returnRequest.processed}
          />
        </div>
        {isBlacklisted && (
          <Alert variant="destructive">
            <AlertTitle>The user is Blacklisted</AlertTitle>
            <AlertDescription>
              <span>
                The user has{' '}
                <span className="inline-block font-bold">{returnCount}</span>{' '}
                past return request(s).
              </span>
            </AlertDescription>
          </Alert>
        )}
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="scanner">Barcode Scanner</TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="summary" className="mt-6 space-y-4">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Return Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Order</span>
                    <span>{returnRequest.orderName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Customer Email
                    </span>
                    <span className={isBlacklisted ? 'text-red-500' : ''}>
                      {returnRequest.email}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Return Reason</span>
                    <span className="capitalize">
                      {returnRequest.returnReason.replace(/_/g, ' ')}
                    </span>
                  </div>
                  {returnRequest.exchangeType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Exchange Type
                      </span>
                      <span>{returnRequest.exchangeType}</span>
                    </div>
                  )}
                  {returnRequest.defectType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Defect Type</span>
                      <span>{returnRequest.defectType}</span>
                    </div>
                  )}
                  {returnRequest.defectDetails && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Defect Details
                      </span>
                      <span>{returnRequest.defectDetails}</span>
                    </div>
                  )}
                  {returnRequest.returnLabelOption && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Return Label Option
                      </span>
                      <span className="capitalize">
                        {returnRequest.returnLabelOption}
                      </span>
                    </div>
                  )}
                  {returnRequest.refundFee !== null && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Refund Fee</span>
                      <span>${returnRequest.refundFee.toFixed(2)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Status Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status</span>
                      <span
                        className={cn(
                          returnRequest.status === 'completed'
                            ? 'text-green-500'
                            : returnRequest.status === 'approved'
                              ? 'text-blue-500'
                              : returnRequest.status === 'rejected'
                                ? 'text-red-500'
                                : 'text-yellow-500',
                          'capitalize'
                        )}
                      >
                        {returnRequest.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Processed</span>
                      <span
                        className={
                          returnRequest.processed === 'completed'
                            ? 'text-green-500'
                            : 'text-red-500'
                        }
                      >
                        {title(returnRequest.processed)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Created</span>
                      <span>{formatDateTime(returnRequest.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Last Updated
                      </span>
                      <span>{formatDateTime(returnRequest.updatedAt)}</span>
                    </div>
                    {returnRequest.adminNotes && (
                      <div className="mt-4">
                        <span className="text-muted-foreground">
                          Admin Notes
                        </span>
                        <p className="mt-1 whitespace-pre-wrap">
                          {returnRequest.adminNotes}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                {returnRequest.trackingNumber && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Tracking Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Tracking Number
                        </span>
                        <span>{returnRequest.trackingNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Tracking Company
                        </span>
                        <span>{returnRequest.trackingCompanyName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="min-w-[140px] text-muted-foreground">
                          Tracking URL
                        </span>
                        <a
                          href={returnRequest.trackingUrl ?? ''}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="overflow-hidden text-ellipsis whitespace-nowrap text-right text-blue-500"
                        >
                          {returnRequest.trackingUrl}
                        </a>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>Return Items</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Variant</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Return Reason</TableHead>
                      <TableHead>Exchange Variant</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {returnRequest.returnItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.title}</TableCell>
                        <TableCell>{item.variantTitle || '-'}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>
                          {item.price.toFixed(2)} {item.currency}
                        </TableCell>
                        <TableCell>{title(item.returnReason) || '-'}</TableCell>
                        <TableCell>{item.exchangeVariant || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Defect Photos</CardTitle>
              </CardHeader>
              <CardContent>
                {returnRequest.defectPhotos.length === 0 ? (
                  <p className="text-muted-foreground">
                    No defect photos provided
                  </p>
                ) : (
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                    {returnRequest.defectPhotos.map((photo) => (
                      <ReturnRequestImageDialog
                        key={photo.id}
                        photo={photo.url}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="scanner" className="mt-6">
            <BarcodeScannerTab returnRequest={returnRequest} />
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
