'use client';

import { signOut } from '@repo/auth/client';
import { UserButton } from '@repo/auth/components/user-button';
import type { User } from '@repo/database/types';
import {
  AnchorIcon,
  ArrowRightLeftIcon,
  FileTextIcon,
  QrCode,
  ReceiptTextIcon,
  SendIcon,
  Settings2Icon,
  UsersRoundIcon,
} from '@repo/design-system/components/icons';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import { Collapsible } from '@repo/design-system/components/ui/collapsible';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@repo/design-system/components/ui/sidebar';
import type { Dictionary } from '@repo/internationalization';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import type { ReactNode } from 'react';

type GlobalSidebarProperties = {
  user?: User;
  dictionary: Dictionary;
  readonly children: ReactNode;
};

export const GlobalSidebar = ({
  user,
  dictionary,
  children,
}: GlobalSidebarProperties) => {
  const data = {
    user: {
      name: 'shadcn',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
    },
    navMain: [
      {
        title: dictionary.admin.navigation.return_requests,
        url: '/return-requests',
        icon: AnchorIcon,
        isActive: true,
      },
      {
        title: dictionary.admin.navigation.return_scanner,
        url: '/return-scanner',
        icon: QrCode,
      },
      {
        title: dictionary.admin.navigation.exchange_requests,
        url: '/exchange-requests',
        icon: ArrowRightLeftIcon,
      },
      {
        title: dictionary.admin.navigation.exchange_scanner,
        url: '/exchange-scanner',
        icon: QrCode,
      },
      {
        title: dictionary.admin.navigation.address_changes,
        url: '/address-changes',
        icon: SendIcon,
      },
      {
        title: dictionary.admin.navigation.receipt_customizations,
        url: '/receipt-customizations',
        icon: ReceiptTextIcon,
      },
    ],
    navAdmin: [
      {
        title: dictionary.admin.navigation.users,
        url: '/admin/users',
        icon: UsersRoundIcon,
      },
      {
        title: dictionary.admin.navigation.cms_content,
        url: '/admin/cms',
        icon: FileTextIcon,
      },
    ],
    navSecondary: [
      {
        title: dictionary.admin.navigation.settings,
        url: '/settings',
        icon: Settings2Icon,
      },
    ],
  };
  useSidebar(); // Required for sidebar context
  const router = useRouter();
  const pathname = usePathname();

  return (
    <>
      <Sidebar variant="inset">
        <SidebarHeader>{/* Header content if needed */}</SidebarHeader>
        <SidebarContent>
          {user?.role === 'super-admin' && (
            <SidebarGroup>
              <SidebarGroupLabel>
                {dictionary.admin.navigation.platform}
              </SidebarGroupLabel>
              <SidebarMenu>
                {data.navAdmin.map((item) => (
                  <Collapsible
                    key={item.title}
                    asChild
                    defaultOpen={pathname.startsWith(item.url)}
                  >
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        asChild
                        tooltip={item.title}
                        isActive={pathname === item.url}
                      >
                        <Link href={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </Collapsible>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          )}
          <SidebarGroup>
            <SidebarGroupLabel>
              {dictionary.admin.navigation.system}
            </SidebarGroupLabel>
            <SidebarMenu>
              {data.navMain.map((item) => (
                <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={item.isActive}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroup>
          <SidebarGroup className="mt-auto">
            <SidebarGroupContent>
              <SidebarMenu>
                {data.navSecondary.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={pathname === item.url}>
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem className="flex items-center gap-2">
              <UserButton
                user={user}
                showName
                onSignOut={() => {
                  signOut({
                    fetchOptions: {
                      onSuccess: () => {
                        router.push('/sign-in');
                      },
                    },
                  });
                }}
                className="flex-1"
              />
              <div className="flex shrink-0 items-center gap-px">
                <ModeToggle />
                {/* <Button
                  variant="ghost"
                  size="icon"
                  className="shrink-0"
                  asChild
                >
                  <div className="h-4 w-4">
                    <NotificationsTrigger />
                  </div>
                </Button> */}
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>{children}</SidebarInset>
    </>
  );
};
