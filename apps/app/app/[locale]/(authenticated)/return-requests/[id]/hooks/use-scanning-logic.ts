import { useCallback, useEffect, useRef, useState } from 'react';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number;
}

interface ScanResult {
  success: boolean;
  message: string;
  itemId?: string;
}

interface UseScanningLogicProps {
  returnItems: ReturnItem[];
  returnNumber: string;
  initialScanningStep?: 'package' | 'items';
  onItemScanned?: (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => void;
}

export function useScanningLogic({
  returnItems,
  returnNumber,
  initialScanningStep,
  onItemScanned,
}: UseScanningLogicProps) {
  const [productChecklist, setProductChecklist] = useState<ReturnItem[]>([]);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioEnabled, setAudioEnabled] = useState(true);

  // Return-specific state
  const [returnPackageVerified, setReturnPackageVerified] = useState(
    initialScanningStep === 'items'
  );
  const [scanningStep, setScanningStep] = useState<'package' | 'items'>(
    initialScanningStep ?? 'package'
  );

  // Audio refs
  const errorAudio = useRef<HTMLAudioElement>(null);
  const successAudio = useRef<HTMLAudioElement>(null);

  // Initialize product checklist from return items
  useEffect(() => {
    const initialChecklist = returnItems.map((item) => ({
      ...item,
      currentQty: 0,
    }));
    setProductChecklist(initialChecklist);
  }, [returnItems]);

  // Initialize audio elements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      errorAudio.current = new Audio('/audio/error.mp3');
      successAudio.current = new Audio('/audio/success.mp3');
    }
  }, []);

  const playAudio = useCallback(
    (isSuccess: boolean) => {
      if (!audioEnabled) {
        return;
      }

      try {
        if (isSuccess && successAudio.current) {
          successAudio.current.play();
        } else if (!isSuccess && errorAudio.current) {
          errorAudio.current.play();
        }
      } catch {
        // Audio playback failed - this is not critical
      }
    },
    [audioEnabled]
  );

  const handleReturnPackageVerification = useCallback(
    (barcode: string) => {
      setErrorMessage('');

      // Check if scanned barcode matches the return number
      if (barcode.toLowerCase() === returnNumber.toLowerCase()) {
        setReturnPackageVerified(true);
        setScanningStep('items');
        playAudio(true);
        setScanResult({
          success: true,
          message: 'Return package verified! Now scan individual items.',
        });
      } else {
        playAudio(false);
        setErrorMessage('Scanned barcode does not match the return number');
        setScanResult({
          success: false,
          message: 'Scanned barcode does not match the return number',
        });
      }
    },
    [returnNumber, playAudio]
  );

  const handleCheckProduct = useCallback(
    (barcode: string) => {
      setErrorMessage('');
      const index = productChecklist.findIndex(
        (item) =>
          item.barcode && item.barcode.toLowerCase() === barcode.toLowerCase()
      );

      if (index !== -1) {
        const newList = [...productChecklist];
        const currentQty = newList[index].currentQty || 0;
        if (currentQty + 1 <= newList[index].quantity) {
          newList[index].currentQty = currentQty + 1;
          setProductChecklist(newList);
          onItemScanned?.(
            newList[index].id,
            barcode,
            newList[index].currentQty
          );
          playAudio(true);
          setScanResult({
            success: true,
            message: `Successfully scanned: ${newList[index].title}`,
            itemId: newList[index].id,
          });
          return;
        }
        setErrorMessage('Maximum amount reached');
        playAudio(false);
        setScanResult({
          success: false,
          message: 'Maximum amount reached',
        });
        return;
      }
      playAudio(false);
      setErrorMessage('Scanned barcode does not match any product in the list');
      setScanResult({
        success: false,
        message: 'Scanned barcode does not match any product in the list',
      });
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleExchangeScanning = useCallback(
    (scannedValue: string, step: 'outgoing' | 'incoming') => {
      setErrorMessage('');

      // Find matching item by SKU or barcode
      const matchingItem = productChecklist.find(
        (item) => item.sku === scannedValue || item.barcode === scannedValue
      );

      if (
        matchingItem &&
        (matchingItem.currentQty || 0) < matchingItem.quantity
      ) {
        // Update the scanned quantity
        setProductChecklist((prev) =>
          prev.map((item) =>
            item.id === matchingItem.id
              ? { ...item, currentQty: (item.currentQty || 0) + 1 }
              : item
          )
        );

        playAudio(true);
        setScanResult({
          success: true,
          message: `${step === 'outgoing' ? 'Outgoing item scanned' : 'Incoming item received'}: ${matchingItem.title}`,
          itemId: matchingItem.id,
        });

        onItemScanned?.(matchingItem.id, scannedValue, 1);
      } else if (
        matchingItem &&
        (matchingItem.currentQty || 0) >= matchingItem.quantity
      ) {
        playAudio(false);
        setErrorMessage(
          step === 'outgoing'
            ? 'This item has already been fully scanned for outgoing shipment'
            : 'This item has already been fully received'
        );
        setScanResult({
          success: false,
          message:
            step === 'outgoing'
              ? 'Item already fully scanned'
              : 'Item already fully received',
        });
      } else {
        playAudio(false);
        setErrorMessage(
          step === 'outgoing'
            ? 'Scanned item does not match any outgoing exchange items'
            : 'Scanned item does not match any expected incoming items'
        );
        setScanResult({
          success: false,
          message:
            step === 'outgoing'
              ? 'Item not found in outgoing exchange list'
              : 'Item not found in expected incoming list',
        });
      }
    },
    [productChecklist, onItemScanned, playAudio]
  );

  const handleMarkAsScanned = useCallback(
    (itemBarcode: string) => {
      const index = productChecklist.findIndex(
        (item) => item.barcode === itemBarcode
      );
      if (index !== -1) {
        const newList = [...productChecklist];
        newList[index].currentQty = newList[index].quantity;
        setProductChecklist(newList);
        onItemScanned?.(
          newList[index].id,
          itemBarcode,
          newList[index].quantity
        );
      }
    },
    [productChecklist, onItemScanned]
  );

  const resetChecklist = useCallback(() => {
    setProductChecklist(
      returnItems.map((item) => ({ ...item, currentQty: 0 }))
    );
    setErrorMessage('');
    setScanResult(null);
  }, [returnItems]);

  // Calculate progress
  const scannedCount = productChecklist.filter(
    (item) => item.currentQty === item.quantity
  ).length;
  const totalCount = productChecklist.length;
  const allScanned = scannedCount === totalCount && totalCount > 0;

  return {
    // State
    productChecklist,
    scanResult,
    errorMessage,
    audioEnabled,
    returnPackageVerified,
    scanningStep,
    scannedCount,
    totalCount,
    allScanned,

    // Actions
    setAudioEnabled,
    setScanResult,
    setErrorMessage,
    handleReturnPackageVerification,
    handleCheckProduct,
    handleExchangeScanning,
    handleMarkAsScanned,
    resetChecklist,
  };
}
