'use client';

interface ProgressIndicatorProps {
  exchangeType: string | null;
  scanningStep?: 'package' | 'items';
  returnPackageVerified?: boolean;
  scannedCount: number;
  totalCount: number;
}

export function ProgressIndicator({
  exchangeType,
  scanningStep,
  returnPackageVerified,
  scannedCount,
  totalCount,
}: ProgressIndicatorProps) {
  const getProgressWidth = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return returnPackageVerified ? '50%' : '0%';
    }
    return `${(scannedCount / totalCount) * 100}%`;
  };

  return (
    <div className="h-2 w-full rounded-full bg-gray-200">
      <div
        className="h-2 rounded-full bg-blue-600 transition-all duration-300"
        style={{ width: getProgressWidth() }}
      />
    </div>
  );
}
