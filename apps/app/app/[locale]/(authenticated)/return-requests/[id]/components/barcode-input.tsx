'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface BarcodeInputProps {
  exchangeType: string | null;
  exchangeStep?: 'outgoing' | 'incoming';
  scanningStep?: 'package' | 'items';
  returnNumber: string;
  onScan: (value: string) => void;
  disabled?: boolean;
}

export function BarcodeInput({
  exchangeType,
  exchangeStep,
  scanningStep,
  returnNumber,
  onScan,
  disabled = false,
}: BarcodeInputProps) {
  const [barcode, setBarcode] = useState('');
  const [delayDebounce, setDelayDebounce] = useState<NodeJS.Timeout>();
  const barcodeInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus the barcode input when component mounts
  useEffect(() => {
    if (barcodeInputRef.current && !disabled) {
      barcodeInputRef.current.focus();
    }
  }, [disabled]);

  // Keep focus on barcode input after scanning
  useEffect(() => {
    if (barcodeInputRef.current && !barcode && !disabled) {
      barcodeInputRef.current.focus();
    }
  }, [barcode, disabled]);

  const handleInput = useCallback(
    (value: string) => {
      clearTimeout(delayDebounce);
      if (value.trim()) {
        const delayDebounceTimeout = setTimeout(() => {
          onScan(value.trim());
          setBarcode('');
          // Refocus the input after processing
          if (barcodeInputRef.current) {
            barcodeInputRef.current.focus();
          }
        }, 300);
        setDelayDebounce(delayDebounceTimeout);
      }
    },
    [delayDebounce, onScan]
  );

  const handleBarcodeSubmit = () => {
    if (!barcode.trim()) {
      return;
    }
    clearTimeout(delayDebounce);
    onScan(barcode.trim());
    setBarcode('');
    // Refocus the input after manual submission
    if (barcodeInputRef.current) {
      barcodeInputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleBarcodeSubmit();
    }
  };

  const getInputLabel = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return 'Return Package Scanner';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return 'Outgoing Item Scanner';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return 'Incoming Item Scanner';
    }
    return 'Barcode Scanner Input';
  };

  const getPlaceholder = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return `Scan return package label (${returnNumber})...`;
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return 'Scan outgoing exchange items...';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return 'Scan incoming items from customer...';
    }
    return 'Focus here and scan barcode...';
  };

  const getHelpText = () => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      return 'Scan the return package label to verify the return before scanning individual items.';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return 'Scan each item being shipped out for the exchange.';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return 'Scan each item received back from the customer.';
    }
    return 'Keep this input focused and use your external barcode scanner to scan items.';
  };

  const shouldShowInput =
    exchangeType !== 'exchange' ||
    (exchangeType === 'exchange' &&
      (exchangeStep === 'outgoing' || exchangeStep === 'incoming'));

  if (!shouldShowInput) {
    return null;
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="barcode-input" className="font-medium text-sm">
        {getInputLabel()}
      </Label>
      <div className="flex gap-2">
        <Input
          ref={barcodeInputRef}
          id="barcode-input"
          type="text"
          value={barcode}
          onChange={(e) => {
            setBarcode(e.target.value);
            handleInput(e.target.value);
          }}
          placeholder={getPlaceholder()}
          className="flex-1 rounded-md border border-gray-300 px-3 py-2 font-mono"
          onKeyDown={handleKeyDown}
          autoComplete="off"
          disabled={disabled}
          autoFocus
        />
        <Button
          onClick={handleBarcodeSubmit}
          disabled={!barcode.trim() || disabled}
          variant="outline"
        >
          Submit
        </Button>
      </div>
      <p className="text-gray-500 text-sm">{getHelpText()}</p>
    </div>
  );
}
