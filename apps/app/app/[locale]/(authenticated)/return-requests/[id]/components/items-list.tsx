'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { CheckCircle, XCircle } from 'lucide-react';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number;
}

interface ItemsListProps {
  items: ReturnItem[];
  exchangeType: string | null;
  exchangeStep?: 'outgoing' | 'incoming';
  scanningStep?: 'package' | 'items';
  onMarkAsScanned?: (itemBarcode: string) => void;
}

export function ItemsList({
  items,
  exchangeType,
  exchangeStep,
  scanningStep,
  onMarkAsScanned,
}: ItemsListProps) {
  const shouldShowList =
    (exchangeType !== 'exchange' &&
      (exchangeType !== 'return' || scanningStep === 'items')) ||
    (exchangeType === 'exchange' &&
      (exchangeStep === 'outgoing' || exchangeStep === 'incoming'));

  if (!shouldShowList) {
    return null;
  }

  const getListTitle = () => {
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return 'Outgoing Exchange Items';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return 'Incoming Exchange Items';
    }
    return 'Return Items';
  };

  return (
    <div className="space-y-2">
      <h4 className="font-medium">{getListTitle()}</h4>
      {items.map((product) => {
        const scanSuccess = product.currentQty === product.quantity;

        return (
          <div
            key={product.id}
            className={`rounded-lg border p-3 ${
              scanSuccess
                ? 'border-green-200 bg-green-50'
                : 'border-gray-200 bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="font-medium">{product.title}</p>
                {product.sku && (
                  <p className="text-gray-600 text-sm">SKU: {product.sku}</p>
                )}
                {product.barcode && (
                  <p className="text-gray-600 text-sm">
                    Barcode: {product.barcode}
                  </p>
                )}
                <p className="text-gray-600 text-sm">Qty: {product.quantity}</p>
                <div className="mt-1 flex items-center gap-2">
                  <span
                    className={`text-sm ${
                      scanSuccess ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {product.currentQty} / {product.quantity} scanned
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <Badge variant={scanSuccess ? 'default' : 'secondary'}>
                  {scanSuccess ? 'Complete' : 'Pending'}
                </Badge>
                {scanSuccess ? (
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Success</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1 text-red-600">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">Scan to Match</span>
                    </div>
                    {product.barcode && onMarkAsScanned && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          onMarkAsScanned(product.barcode as string)
                        }
                        disabled={scanSuccess}
                      >
                        Mark as scanned
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
