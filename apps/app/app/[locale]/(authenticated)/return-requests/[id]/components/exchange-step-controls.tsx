'use client';

import { Button } from '@repo/design-system/components/ui/button';

interface ExchangeStepControlsProps {
  exchangeStep: 'outgoing' | 'incoming';
  processed?: string;
  onStepChange: (step: 'outgoing' | 'incoming') => void;
}

export function ExchangeStepControls({
  exchangeStep,
  processed,
  onStepChange,
}: ExchangeStepControlsProps) {
  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <Button
          variant={exchangeStep === 'outgoing' ? 'default' : 'outline'}
          onClick={() => onStepChange('outgoing')}
          disabled={
            processed === 'exchange_shipped' ||
            processed === 'returned_received'
          }
          className="flex-1"
        >
          Outgoing Items
        </Button>
        <Button
          variant={exchangeStep === 'incoming' ? 'default' : 'outline'}
          onClick={() => onStepChange('incoming')}
          disabled={processed === 'pending'}
          className="flex-1"
        >
          Incoming Items
        </Button>
      </div>
      <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 text-blue-800">
        <p className="text-sm">
          {exchangeStep === 'outgoing'
            ? 'Scan items being shipped out to the customer for exchange'
            : 'Scan items received back from the customer'}
        </p>
      </div>
    </div>
  );
}
