'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { useToast } from '@repo/design-system/components/ui/use-toast';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number;
}

interface SubmitButtonProps {
  exchangeType: string | null;
  exchangeStep?: 'outgoing' | 'incoming';
  scanningStep?: 'package' | 'items';
  returnPackageVerified?: boolean;
  allScanned: boolean;
  productChecklist: ReturnItem[];
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
}

export function SubmitButton({
  exchangeType,
  exchangeStep,
  returnPackageVerified,
  allScanned,
  productChecklist,
  onSubmit,
}: SubmitButtonProps) {
  const { toast } = useToast();

  const shouldShowButton =
    ((exchangeType !== 'exchange' &&
      (exchangeType !== 'return' || (returnPackageVerified && allScanned))) ||
      (exchangeType === 'exchange' && allScanned)) &&
    onSubmit;

  if (!shouldShowButton) {
    return null;
  }

  const getButtonText = () => {
    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      return 'Confirm Items & Mark as Exchange Shipped';
    }
    if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      return 'Confirm Received Items & Mark as Returned Received';
    }
    return 'Complete Verification';
  };

  const handleSubmit = () => {
    const scannedItems = productChecklist.map((item) => ({
      itemId: item.id,
      scannedQuantity: item.currentQty || 0,
    }));

    onSubmit?.(scannedItems);

    let title = 'Success';
    let description = 'All items have been verified successfully!';

    if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      title = 'Outgoing Items Confirmed';
      description =
        'All outgoing exchange items have been scanned and confirmed for shipment.';
    } else if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      title = 'Incoming Items Received';
      description =
        'All incoming exchange items have been received and confirmed.';
    }

    toast({
      title,
      description,
    });
  };

  return (
    <div className="flex justify-center pt-4">
      <Button onClick={handleSubmit} className="w-full">
        {getButtonText()}
      </Button>
    </div>
  );
}
