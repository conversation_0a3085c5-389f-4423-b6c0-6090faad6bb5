'use client';

import { CheckCircle, XCircle } from 'lucide-react';

interface ScanResultProps {
  scanResult: {
    success: boolean;
    message: string;
    itemId?: string;
  } | null;
  errorMessage?: string;
}

export function ScanResult({ scanResult, errorMessage }: ScanResultProps) {
  return (
    <>
      {/* Error message */}
      {errorMessage && (
        <div className="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
          <XCircle className="h-4 w-4" />
          <span className="text-sm">{errorMessage}</span>
        </div>
      )}

      {/* Scan result */}
      {scanResult && (
        <div
          className={`flex items-center gap-2 rounded-lg p-3 ${
            scanResult.success
              ? 'border border-green-200 bg-green-50 text-green-800'
              : 'border border-red-200 bg-red-50 text-red-800'
          }`}
        >
          {scanResult.success ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <XCircle className="h-4 w-4" />
          )}
          <span className="text-sm">{scanResult.message}</span>
        </div>
      )}
    </>
  );
}
