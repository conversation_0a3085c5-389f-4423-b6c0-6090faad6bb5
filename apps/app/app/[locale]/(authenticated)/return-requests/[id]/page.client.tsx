'use client';

import {
  Di<PERSON>,
  DialogContent,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import Image from 'next/image';

export function ReturnRequestImageDialog({ photo }: { photo: string }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <button
          type="button"
          className="w-full cursor-pointer overflow-hidden rounded-md text-left"
        >
          <Image
            src={photo}
            alt="Defect photo"
            width={300}
            height={300}
            className="h-auto w-full object-cover transition-all hover:scale-105"
          />
        </button>
      </DialogTrigger>
      <DialogContent className="min-w-4xl max-w-5xl">
        <DialogTitle>Defect Photo</DialogTitle>
        <Image
          src={photo}
          alt="Defect photo"
          width={1200}
          height={1200}
          className="h-auto w-full object-contain"
        />
      </DialogContent>
    </Dialog>
  );
}
