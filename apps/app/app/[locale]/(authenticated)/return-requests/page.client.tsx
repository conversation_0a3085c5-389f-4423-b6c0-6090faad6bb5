'use client';

import type { SerializedReturnRequests } from '@/types';
import type { Dictionary } from '@repo/internationalization';
import { columns } from './components/return-request-column';
import { ReturnRequestTable } from './components/return-request-table';

export function ReturnRequestsPageClient({
  returnRequests,
  dictionary,
}: {
  returnRequests: SerializedReturnRequests[];
  dictionary: Dictionary;
}) {
  return (
    <ReturnRequestTable
      columns={columns(dictionary)}
      initialData={returnRequests}
    />
  );
}
