import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getReturnRequests } from './actions';
import { columns } from './components/return-request-column';
import { ReturnRequestTable } from './components/return-request-table';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.return_requests.title}`,
    description: dictionary.admin.return_requests.description,
  };
}

const ReturnRequestsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const returnRequests = await getReturnRequests();

  return (
    <>
      <Header page={dictionary.admin.return_requests.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReturnRequestTable
            columns={columns(dictionary)}
            initialData={returnRequests}
          />
        </div>
      </div>
    </>
  );
};

export default ReturnRequestsPage;
