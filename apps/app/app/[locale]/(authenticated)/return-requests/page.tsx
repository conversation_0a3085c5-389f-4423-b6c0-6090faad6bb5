import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getReturnRequests } from './actions';
import { ReturnRequestsPageClient } from './page.client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.return_requests.title}`,
    description: dictionary.admin.return_requests.description,
  };
}

const ReturnRequestsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const returnRequests = await getReturnRequests();

  return (
    <>
      <Header page={dictionary.admin.return_requests.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReturnRequestsPageClient {...{ returnRequests, dictionary }} />
        </div>
      </div>
    </>
  );
};

export default ReturnRequestsPage;
