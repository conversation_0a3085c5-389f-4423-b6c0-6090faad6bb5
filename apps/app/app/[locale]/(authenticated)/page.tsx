import { auth } from '@repo/auth/server';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { Header } from './components/header';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.dashboard.title}`,
    description: dictionary.admin.dashboard.description,
  };
}

const App = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}) => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    notFound();
  }

  return (
    <>
      <Header page={dictionary.admin.dashboard.title} />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0" />
    </>
  );
};

export default App;
