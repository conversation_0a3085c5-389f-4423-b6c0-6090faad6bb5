'use client';

import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { log } from '@repo/observability/log';
import { CheckCircle, Volume2, VolumeX } from 'lucide-react';
import { title } from 'radash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { BarcodeScanner } from '../return-requests/[id]/barcode-scanner';
import { updateReturnRequest } from '../return-requests/actions';
import { findReturnByTracking } from './action';

interface ReturnRequest {
  id: string;
  returnNumber: string;
  processed: string;
  exchangeType: string | null;
  orderName: string;
  email: string;
  returnItems: Array<{
    id: string;
    title: string;
    sku: string | null;
    barcode: string | null;
    quantity: number;
  }>;
}

export function ExchangeScannerClient() {
  const { toast } = useToast();
  const [trackingNumber, setTrackingNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [foundRequest, setFoundRequest] = useState<ReturnRequest | null>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioEnabled, setAudioEnabled] = useState(true);
  const trackingInputRef = useRef<HTMLInputElement>(null);

  // Audio feedback
  const errorAudio = useRef<HTMLAudioElement>(null);
  const successAudio = useRef<HTMLAudioElement>(null);

  // Initialize audio elements
  useEffect(() => {
    if (typeof window !== 'undefined') {
      errorAudio.current = new Audio('/audio/error.mp3');
      successAudio.current = new Audio('/audio/success.mp3');
    }
  }, []);

  const playAudio = useCallback(
    (isSuccess: boolean) => {
      if (!audioEnabled) {
        return;
      }

      try {
        if (isSuccess && successAudio.current) {
          successAudio.current.play();
        } else if (!isSuccess && errorAudio.current) {
          errorAudio.current.play();
        }
      } catch {
        // Audio playback failed - this is not critical
      }
    },
    [audioEnabled]
  );

  const searchByTrackingNumber = useCallback(
    async (tracking: string, loading = false) => {
      if (!tracking.trim()) {
        return;
      }

      setErrorMessage('');

      if (loading) {
        setIsLoading(true);
        setFoundRequest(null);
      }

      try {
        const returnRequest = await findReturnByTracking(tracking.trim());

        if (returnRequest) {
          setFoundRequest(returnRequest);

          playAudio(true);

          toast({
            title: 'Exchange Request Found',
            description: `Found exchange request ${returnRequest.returnNumber}`,
          });
        } else {
          setErrorMessage(
            'No exchange request found for this tracking number.'
          );

          playAudio(false);

          toast({
            title: 'Not Found',
            description: 'No exchange request found for this tracking number.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error searching for return request:', error);
        setErrorMessage('An error occurred while searching. Please try again.');
        playAudio(false);
        toast({
          title: 'Error',
          description: 'An error occurred while searching. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    },
    [toast, playAudio]
  );

  const handleTrackingInput = useCallback(
    (value: string) => {
      setTrackingNumber(value);
      // Auto-search when tracking number looks complete (adjust length as needed)
      if (value.trim().length >= 4) {
        searchByTrackingNumber(value, true);
      }
    },
    [searchByTrackingNumber]
  );

  const handleSubmit = () => {
    if (trackingNumber.trim()) {
      searchByTrackingNumber(trackingNumber.trim(), true);
    }
  };

  const handleProcessRequest = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    if (!foundRequest) {
      return;
    }

    log.info('All items verified:', scannedItems);

    // Determine the next processed status based on exchange type and current status
    let newProcessedStatus = 'completed';

    if (foundRequest.exchangeType === 'exchange') {
      if (foundRequest.processed === 'pending') {
        newProcessedStatus = 'exchange_shipped';
      } else if (foundRequest.processed === 'exchange_shipped') {
        newProcessedStatus = 'completed';
      }
    }

    log.info(`Updating return request status to: ${newProcessedStatus}`);

    await updateReturnRequest(foundRequest.id, {
      processed: newProcessedStatus,
    });

    searchByTrackingNumber(trackingNumber.trim());
  };

  return (
    <div className="space-y-6 py-4">
      <Card>
        <CardHeader className="flex items-center gap-2">
          <div>
            <CardTitle>Yamato Tracking Number Scanner</CardTitle>
            <CardDescription>
              Scan or enter a Yamato tracking number to find the associated
              exchange request
            </CardDescription>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="ml-auto flex items-center gap-2"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
            {audioEnabled ? 'Audio On' : 'Audio Off'}
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tracking-input" className="font-medium text-sm">
              Yamato Tracking Number
            </Label>
            <div className="flex gap-2">
              <Input
                ref={trackingInputRef}
                id="tracking-input"
                type="text"
                value={trackingNumber}
                onChange={(e) => handleTrackingInput(e.target.value)}
                placeholder="Focus here and scan or enter tracking number..."
                className="flex-1"
                autoFocus
              />
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !trackingNumber.trim()}
              >
                {isLoading ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>

          {errorMessage && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3 text-red-800">
              {errorMessage}
            </div>
          )}
        </CardContent>
      </Card>

      {foundRequest && (
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">
              Exchange Request Found
            </CardTitle>
            <CardDescription>
              Return Number: {foundRequest.returnNumber} | Status:{' '}
              {title(foundRequest.processed)}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Order:</strong> {foundRequest.orderName}
              </div>
              <div>
                <strong>Email:</strong> {foundRequest.email}
              </div>
              <div>
                <strong>Type:</strong> {title(foundRequest.exchangeType)}
              </div>
              <div>
                <strong>Items:</strong> {foundRequest.returnItems.length}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Return Items:</h4>
              <ul className="space-y-1 text-sm">
                {foundRequest.returnItems.map((item) => (
                  <li key={item.id} className="flex justify-between">
                    <span>{item.title}</span>
                    <span>Qty: {item.quantity}</span>
                  </li>
                ))}
              </ul>
            </div>

            {foundRequest.processed === 'completed' ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {foundRequest.exchangeType === 'exchange'
                      ? 'Exchange Processing'
                      : 'Return Processing'}
                  </CardTitle>
                  <CardDescription className="mt-2 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    {foundRequest.exchangeType === 'exchange'
                      ? 'Exchange completed successfully'
                      : 'Return processed successfully'}
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <BarcodeScanner
                returnItems={foundRequest.returnItems}
                exchangeType={foundRequest.exchangeType}
                returnNumber={foundRequest.returnNumber}
                processed={foundRequest.processed}
                onSubmit={handleProcessRequest}
              />
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
