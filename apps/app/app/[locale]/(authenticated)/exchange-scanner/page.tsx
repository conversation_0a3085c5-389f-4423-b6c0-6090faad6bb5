import type { Metadata } from 'next';
import { Header } from '../components/header';
import { ExchangeScannerClient } from './exchange-scanner-client';

const title = 'Senders Return - Exchange Scanner';
const description = 'Scan Yamato tracking numbers to find exchange requests';

export const metadata: Metadata = {
  title,
  description,
};

const ExchangeScannerPage = () => {
  return (
    <>
      <Header page="Exchange Scanner" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ExchangeScannerClient />
        </div>
      </div>
    </>
  );
};

export default ExchangeScannerPage;
