import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { Header } from '../components/header';
import { ExchangeScannerClient } from './exchange-scanner-client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.navigation.exchange_scanner}`,
    description: 'Scan Yamato tracking numbers to find exchange requests',
  };
}

const ExchangeScannerPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}) => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return (
    <>
      <Header page={dictionary.admin.navigation.exchange_scanner} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ExchangeScannerClient />
        </div>
      </div>
    </>
  );
};

export default ExchangeScannerPage;
