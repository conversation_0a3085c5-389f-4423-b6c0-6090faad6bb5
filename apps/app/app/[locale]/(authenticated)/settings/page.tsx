import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { AutoApprovalSettings } from './auto-approval-settings';
import { BlacklistSettings } from './blacklist-settings';

const title = 'Senders Return - Settings';
const description = 'Manage application settings and configurations';

export const metadata: Metadata = {
  title,
  description,
};

const SettingsPage = (): ReactElement => {
  return (
    <>
      <Header page="Settings" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="space-y-6">
          <div>
            <h2 className="font-bold text-2xl tracking-tight">Settings</h2>
            <p className="text-muted-foreground">
              Manage your application settings and configurations.
            </p>
          </div>

          <BlacklistSettings />
          <AutoApprovalSettings />
        </div>
      </div>
    </>
  );
};

export default SettingsPage;
