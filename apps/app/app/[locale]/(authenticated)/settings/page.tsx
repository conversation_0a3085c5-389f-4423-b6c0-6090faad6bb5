import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { AutoApprovalSettings } from './auto-approval-settings';
import { BlacklistSettings } from './blacklist-settings';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.settings.title}`,
    description: dictionary.admin.settings.description,
  };
}

const SettingsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return (
    <>
      <Header page={dictionary.admin.settings.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="space-y-6">
          <div>
            <h2 className="font-bold text-2xl tracking-tight">
              {dictionary.admin.settings.title}
            </h2>
            <p className="text-muted-foreground">
              {dictionary.admin.settings.description}
            </p>
          </div>

          <BlacklistSettings dictionary={dictionary} />
          <AutoApprovalSettings dictionary={dictionary} />
        </div>
      </div>
    </>
  );
};

export default SettingsPage;
