'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import type { Dictionary } from '@repo/internationalization';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { getAutoApprovalDays, updateAutoApprovalDays } from './actions';

type AutoApprovalSettingsProps = {
  dictionary: Dictionary;
};

export function AutoApprovalSettings({
  dictionary,
}: AutoApprovalSettingsProps) {
  // Form validation schema
  const autoApprovalSchema = z.object({
    days: z
      .number()
      .min(1, dictionary.admin.settings.auto_approval.days_min_error)
      .max(365, dictionary.admin.settings.auto_approval.days_max_error),
  });

  type AutoApprovalFormValues = z.infer<typeof autoApprovalSchema>;
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Initialize form with react-hook-form
  const form = useForm<AutoApprovalFormValues>({
    resolver: zodResolver(autoApprovalSchema),
    defaultValues: {
      days: 30,
    },
  });

  useEffect(() => {
    loadAutoApprovalDays();
  }, []);

  const loadAutoApprovalDays = async () => {
    try {
      const currentDays = await getAutoApprovalDays();
      form.setValue('days', currentDays);
    } catch (error) {
      console.error('Failed to load auto-approval days:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (values: AutoApprovalFormValues) => {
    try {
      await updateAutoApprovalDays(values.days);
      form.reset(values); // Reset form to current values to clear dirty state
      toast({
        title: dictionary.admin.common.success,
        description: dictionary.admin.settings.auto_approval.save_success,
      });
    } catch (error) {
      console.error('Failed to save auto-approval days:', error);
      toast({
        title: dictionary.admin.common.error,
        description: dictionary.admin.settings.auto_approval.save_error,
        variant: 'destructive',
      });
    }
  };

  const handleReset = () => {
    form.reset();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.admin.settings.auto_approval.title}</CardTitle>
        <CardDescription>
          {dictionary.admin.settings.auto_approval.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        ) : (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSave)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {dictionary.admin.settings.auto_approval.days_label}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="365"
                        className="w-32"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      {dictionary.admin.settings.auto_approval.days_description.replace(
                        '{days}',
                        form.watch('days').toString()
                      )}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.formState.isDirty && (
                <div className="flex gap-2 border-t pt-4">
                  <Button
                    type="submit"
                    disabled={form.formState.isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {form.formState.isSubmitting ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : null}
                    {dictionary.admin.settings.auto_approval.save_changes}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    disabled={form.formState.isSubmitting}
                  >
                    {dictionary.admin.settings.auto_approval.reset}
                  </Button>
                </div>
              )}
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
