'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { formatDate } from '@repo/design-system/lib/format';
import type { ColumnDef } from '@tanstack/react-table';

// Define the AddressChangeRequest type based on what we're selecting in the action
export type AddressChangeRow = {
  id: string;
  orderName: string;
  email: string;
  status: string;
  createdAt: string;
  updatedAt: string;
};

export const columns: ColumnDef<AddressChangeRow>[] = [
  {
    accessorKey: 'orderName',
    header: 'Order',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('orderName')}</div>
    ),
  },
  {
    accessorKey: 'email',
    header: 'Customer Email',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      return (
        <Badge
          variant={
            status === 'completed'
              ? 'default'
              : status === 'approved'
                ? 'success'
                : status === 'rejected'
                  ? 'destructive'
                  : 'secondary'
          }
        >
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Requested',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
  {
    accessorKey: 'updatedAt',
    header: 'Last Updated',
    cell: ({ row }) => {
      const date = new Date(row.getValue('updatedAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
];
