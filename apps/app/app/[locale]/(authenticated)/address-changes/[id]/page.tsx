import { Header } from '@/app/(authenticated)/components/header';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { dateFormatter } from '@repo/design-system/lib/format';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getAddressChangeRequest } from '../actions';
import { AddressTimeline } from './timeline';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const addressChange = await getAddressChangeRequest(id);

  const title = 'Senders Return - Address Change';
  const description = `Address change for order ${addressChange.orderName}`;

  return {
    title,
    description,
  };
}

export default async function AddressChangeDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const addressChange = await getAddressChangeRequest(id);

  // Parse the JSON address objects
  const originalAddress = addressChange.originalAddress as Record<
    string,
    string
  >;
  const newAddress = addressChange.newAddress as Record<string, string>;

  return (
    <>
      <Header pages={['Address Changes']} page={addressChange.orderName} />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            Address Change for Order {addressChange.orderName}
          </h2>
          {/* <AddressChangeStatusForm
            id={addressChange.id}
            currentStatus={addressChange.status}
          /> */}
        </div>

        <div className="grid gap-4 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Request Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Order</span>
                <span>{addressChange.orderName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Customer Email</span>
                <span>{addressChange.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status</span>
                <span className="capitalize">{addressChange.status}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Requested</span>
                <span>
                  {dateFormatter(
                    new Date(addressChange.createdAt),
                    'MMM d, yyyy h:mm a'
                  )}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last Updated</span>
                <span>
                  {dateFormatter(
                    new Date(addressChange.updatedAt),
                    'MMM d, yyyy h:mm a'
                  )}
                </span>
              </div>
              {addressChange.adminNotes && (
                <div className="mt-4">
                  <span className="text-muted-foreground">Admin Notes</span>
                  <p className="mt-1 whitespace-pre-wrap">
                    {addressChange.adminNotes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Original Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {originalAddress.name && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name</span>
                    <span>{originalAddress.name}</span>
                  </div>
                )}
                {originalAddress.address1 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Address Line 1
                    </span>
                    <span>{originalAddress.address1}</span>
                  </div>
                )}
                {originalAddress.address2 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Address Line 2
                    </span>
                    <span>{originalAddress.address2}</span>
                  </div>
                )}
                {originalAddress.city && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">City</span>
                    <span>{originalAddress.city}</span>
                  </div>
                )}
                {originalAddress.province && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      State/Province
                    </span>
                    <span>{originalAddress.province}</span>
                  </div>
                )}
                {originalAddress.zip && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      ZIP/Postal Code
                    </span>
                    <span>{originalAddress.zip}</span>
                  </div>
                )}
                {originalAddress.country && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Country</span>
                    <span>{originalAddress.country}</span>
                  </div>
                )}
                {originalAddress.phone && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone</span>
                    <span>{originalAddress.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>New Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {newAddress.name && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name</span>
                    <span>{newAddress.name}</span>
                  </div>
                )}
                {newAddress.address1 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Address Line 1
                    </span>
                    <span>{newAddress.address1}</span>
                  </div>
                )}
                {newAddress.address2 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Address Line 2
                    </span>
                    <span>{newAddress.address2}</span>
                  </div>
                )}
                {newAddress.city && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">City</span>
                    <span>{newAddress.city}</span>
                  </div>
                )}
                {newAddress.province && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      State/Province
                    </span>
                    <span>{newAddress.province}</span>
                  </div>
                )}
                {newAddress.zip && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      ZIP/Postal Code
                    </span>
                    <span>{newAddress.zip}</span>
                  </div>
                )}
                {newAddress.country && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Country</span>
                    <span>{newAddress.country}</span>
                  </div>
                )}
                {newAddress.phone && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone</span>
                    <span>{newAddress.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Timeline Section */}
        <AddressTimeline
          addressChange={{
            ...addressChange,
            originalAddress,
            newAddress,
          }}
        />
      </main>
    </>
  );
}
