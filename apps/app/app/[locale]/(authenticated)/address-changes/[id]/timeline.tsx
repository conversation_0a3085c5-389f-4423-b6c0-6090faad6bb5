'use client';

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from '@repo/design-system/components/ui/timeline';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import {} from '@repo/design-system/components/ui/tooltip';
import { dateFormatter } from '@repo/design-system/lib/format';
import { Calendar } from 'lucide-react';

interface Address {
  name?: string;
  firstName?: string;
  lastName?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  zip?: string;
  country?: string;
  phone?: string;
  company?: string;
}

interface TimelineEvent {
  id: string;
  type: 'created' | 'updated' | 'approved' | 'rejected' | 'completed';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  status?: string;
  changes?: string[];
}

interface TimelineProps {
  addressChange: {
    id: string;
    orderName: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    adminNotes: string | null;
    originalAddress?: Address;
    newAddress?: Address;
  };
}

// Helper function to compare addresses and generate change descriptions
function getAddressChanges(
  originalAddress?: Address,
  newAddress?: Address
): string[] {
  if (!originalAddress || !newAddress) {
    return [];
  }

  const changes: string[] = [];
  const fieldLabels: Record<keyof Address, string> = {
    name: 'Name',
    firstName: 'First Name',
    lastName: 'Last Name',
    address1: 'Address Line 1',
    address2: 'Address Line 2',
    city: 'City',
    province: 'State/Province',
    zip: 'ZIP/Postal Code',
    country: 'Country',
    phone: 'Phone',
    company: 'Company',
  };

  for (const field of Object.keys(fieldLabels) as Array<keyof Address>) {
    const oldValue = originalAddress[field];
    const newValue = newAddress[field];

    if (oldValue !== newValue) {
      const label = fieldLabels[field];
      const oldDisplay = oldValue || '(empty)';
      const newDisplay = newValue || '(empty)';
      changes.push(`${label}: ${oldDisplay} → ${newDisplay}`);
    }
  }

  return changes;
}

export function AddressTimeline({ addressChange }: TimelineProps) {
  // Generate timeline events based on address change data
  const generateTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Get address changes for tooltips
    const addressChanges = getAddressChanges(
      addressChange.originalAddress,
      addressChange.newAddress
    );

    // Created event
    events.push({
      id: 'created',
      type: 'created',
      title: 'Address Change Requested',
      description: `Customer requested address change for order ${addressChange.orderName}`,
      timestamp: new Date(addressChange.createdAt),
      user: 'Customer',
      changes: addressChanges,
    });

    // Status change events (simulated based on current status)
    if (addressChange.status === 'approved') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: 'Request Approved',
        description: 'Address change request has been approved by admin',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'approved',
      });
    } else if (addressChange.status === 'rejected') {
      events.push({
        id: 'rejected',
        type: 'rejected',
        title: 'Request Rejected',
        description:
          addressChange.adminNotes ||
          'Address change request has been rejected',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'rejected',
      });
    } else if (addressChange.status === 'completed') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: 'Request Approved',
        description: 'Address change request has been approved by admin',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'approved',
      });
      events.push({
        id: 'completed',
        type: 'completed',
        title: 'Address Updated',
        description:
          'Shipping address has been successfully updated in the system',
        timestamp: new Date(addressChange.updatedAt),
        user: 'System',
        status: 'completed',
      });
    }

    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const events = generateTimelineEvents();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Request Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Timeline defaultValue={3}>
          {events.map((item, index) => {
            const hasChanges = item.changes && item.changes.length > 0;

            const timelineItem = (
              <TimelineItem
                key={item.id}
                step={index}
                className="group-data-[orientation=vertical]/timeline:sm:ms-32"
              >
                <TimelineHeader>
                  <TimelineSeparator />
                  <TimelineDate className="group-data-[orientation=vertical]/timeline:sm:-left-32 group-data-[orientation=vertical]/timeline:sm:absolute group-data-[orientation=vertical]/timeline:sm:w-20 group-data-[orientation=vertical]/timeline:sm:text-right">
                    {dateFormatter(item.timestamp, 'MMM d, yyyy h:mm a')}
                  </TimelineDate>
                  <TimelineTitle className="sm:-mt-0.5">
                    {item.title}
                  </TimelineTitle>
                  <TimelineIndicator />
                </TimelineHeader>
                <TimelineContent>{item.description}</TimelineContent>
              </TimelineItem>
            );

            // If there are changes, wrap with tooltip
            if (hasChanges) {
              return (
                <Tooltip key={item.id}>
                  <TooltipTrigger asChild>{timelineItem}</TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="space-y-1">
                      <div className="font-semibold text-sm">
                        Address Changes:
                      </div>
                      {item.changes?.map((change, changeIndex) => (
                        <div key={changeIndex} className="text-xs">
                          {change}
                        </div>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              );
            }

            return timelineItem;
          })}
        </Timeline>
      </CardContent>
    </Card>
  );
}
