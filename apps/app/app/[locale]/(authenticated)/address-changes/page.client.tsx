'use client';

import type { SerializedAddressChangeRequests } from '@/types';
import type { Dictionary } from '@repo/internationalization';
import { columns } from './components/address-change-column';
import { AddressChangeTable } from './components/address-change-table';

export function AddressChangeRequestsPageClient({
  addressChangeRequests,
  dictionary,
}: {
  addressChangeRequests: SerializedAddressChangeRequests;
  dictionary: Dictionary;
}) {
  return (
    <AddressChangeTable
      columns={columns(dictionary)}
      initialData={addressChangeRequests}
    />
  );
}
