import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getAddressChangeRequests } from './actions';
import { AddressChangeRequestsPageClient } from './page.client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.address_changes.title}`,
    description: dictionary.admin.address_changes.description,
  };
}

const AddressChangeRequestsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const addressChangeRequests = await getAddressChangeRequests();

  return (
    <>
      <Header page={dictionary.admin.address_changes.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <AddressChangeRequestsPageClient
            {...{ addressChangeRequests, dictionary }}
          />
        </div>
      </div>
    </>
  );
};

export default AddressChangeRequestsPage;
