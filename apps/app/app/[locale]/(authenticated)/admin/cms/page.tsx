import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getCmsContents } from './actions';
import { CmsPageClient } from './page.client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.cms.title}`,
    description: dictionary.admin.cms.description,
  };
}

const CmsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const content = await getCmsContents();

  return (
    <>
      <Header page={dictionary.admin.cms.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <CmsPageClient {...{ content, dictionary }} />
        </div>
      </div>
    </>
  );
};

export default CmsPage;
