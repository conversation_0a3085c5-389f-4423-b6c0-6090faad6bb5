'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import type { ColumnDef } from '@tanstack/react-table';
import {} from 'lucide-react';
import type { SerializedCmsContent } from '../actions';

export const columns: ColumnDef<SerializedCmsContent>[] = [
  {
    accessorKey: 'key',
    header: 'Key',
    cell: ({ row }) => {
      const key = row.getValue('key') as string;
      return <div className="font-medium">{key}</div>;
    },
  },
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      const title = row.getValue('title') as string;
      return <div>{title}</div>;
    },
  },
  {
    accessorKey: 'isPublished',
    header: 'Status',
    cell: ({ row }) => {
      const isPublished = row.getValue('isPublished') as boolean;
      return (
        <Badge variant={isPublished ? 'default' : 'secondary'}>
          {isPublished ? 'Published' : 'Draft'}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'updatedAt',
    header: 'Last Updated',
    cell: ({ row }) => {
      const date = new Date(row.getValue('updatedAt') as string);
      return <div>{date.toLocaleString()}</div>;
    },
  },
];
