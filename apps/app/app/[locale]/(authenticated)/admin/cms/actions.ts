'use server';

import { database, serializePrisma } from '@repo/database';
import { revalidatePath } from 'next/cache';

// Define the form data interface for client-server communication
export interface ContentFormData {
  id: string;
  key: string;
  title: string;
  description: string;
  isPublished: boolean;
  content: Record<string, string>;
}

// Define the serialized response type
export type SerializedCmsContent = {
  id: string;
  createdAt: string;
  updatedAt: string;
  key: string;
  title: string;
  description: string | null;
  content: Record<string, string>;
  isPublished: boolean;
};

/**
 * Get a single CMS content item by ID
 */
export async function getCmsContent(
  id: string
): Promise<SerializedCmsContent | null> {
  try {
    const content = await database.cmsContent.findUnique({
      where: { id },
    });

    if (!content) return null;

    // Ensure the content is properly typed
    const serialized = serializePrisma(content);
    return {
      ...serialized,
      content: serialized.content as Record<string, string>,
    };
  } catch (error) {
    console.error('Error fetching CMS content:', error);
    throw new Error('Failed to fetch content');
  }
}

/**
 * Get all CMS content items with optional filtering
 */
export async function getCmsContents(options?: {
  isPublished?: boolean;
  searchQuery?: string;
}): Promise<SerializedCmsContent[]> {
  try {
    const { isPublished, searchQuery } = options || {};

    // Define a more specific type for the where clause
    type WhereClause = {
      isPublished?: boolean;
      OR?: Array<{
        key?: { contains: string; mode: 'insensitive' };
        title?: { contains: string; mode: 'insensitive' };
        description?: { contains: string; mode: 'insensitive' };
      }>;
    };

    const where: WhereClause = {};

    if (isPublished !== undefined) {
      where.isPublished = isPublished;
    }

    if (searchQuery) {
      where.OR = [
        { key: { contains: searchQuery, mode: 'insensitive' } },
        { title: { contains: searchQuery, mode: 'insensitive' } },
        { description: { contains: searchQuery, mode: 'insensitive' } },
      ];
    }

    const contents = await database.cmsContent.findMany({
      where,
      orderBy: { updatedAt: 'desc' },
    });

    // Ensure the content is properly typed
    const serialized = serializePrisma(contents);
    return serialized.map((item) => ({
      ...item,
      content: item.content as Record<string, string>,
    }));
  } catch (error) {
    console.error('Error fetching CMS contents:', error);
    throw new Error('Failed to fetch contents');
  }
}

/**
 * Create or update CMS content
 * This is a server action that can be called from client components
 */
export async function createOrUpdateContent(
  data: ContentFormData
): Promise<{ success: boolean }> {
  try {
    const { id, key, title, description, isPublished, content } = data;

    if (id) {
      // Update existing content
      await database.cmsContent.update({
        where: { id },
        data: {
          key,
          title,
          description,
          isPublished,
          content,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new content
      await database.cmsContent.create({
        data: {
          key,
          title,
          description,
          isPublished,
          content,
        },
      });
    }

    // Revalidate the CMS page to refresh the data
    revalidatePath('/admin/cms');
    return { success: true };
  } catch (error) {
    console.error('Error saving CMS content:', error);
    throw new Error('Failed to save content');
  }
}
