'use client';

import type { SerializedUser } from '@/types';
import type { Dictionary } from '@repo/internationalization';
import { columns } from './components/user-column';
import { UserTable } from './components/user-table';

export function UsersPageClient({
  users,
  dictionary,
}: {
  users: SerializedUser[];
  dictionary: Dictionary;
}) {
  return <UserTable columns={columns(dictionary)} initialData={users} />;
}
