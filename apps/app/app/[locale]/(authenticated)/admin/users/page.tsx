import {} from '@repo/design-system/components/ui/breadcrumb';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getUsers } from './actions';
import { columns } from './components/user-column';
import { UserTable } from './components/user-table';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.users.title}`,
    description: dictionary.admin.users.description,
  };
}

const UsersPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const users = await getUsers();

  return (
    <>
      <Header page={dictionary.admin.users.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <UserTable columns={columns(dictionary)} initialData={users} />
        </div>
      </div>
    </>
  );
};

export default UsersPage;
