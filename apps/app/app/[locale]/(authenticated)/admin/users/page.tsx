import {} from '@repo/design-system/components/ui/breadcrumb';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getUsers } from './actions';
import { columns } from './components/user-column';
import { UserTable } from './components/user-table';

const title = 'Senders Return - Users';
const description = 'Senders Return - Users';

export const metadata: Metadata = {
  title,
  description,
};

const UsersPage = async (): Promise<ReactElement> => {
  const users = await getUsers();

  return (
    <>
      <Header page="Users" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <UserTable columns={columns} initialData={users} />
        </div>
      </div>
    </>
  );
};

export default UsersPage;
