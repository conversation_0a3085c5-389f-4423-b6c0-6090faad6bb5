'use client';

import type { SerializedReceiptCustomizations } from '@/types';
import type { Dictionary } from '@repo/internationalization';
import { columns } from './components/receipt-customization-column';
import { ReceiptCustomizationTable } from './components/receipt-customization-table';

export function ReceiptCustomizationsPageClient({
  receiptCustomizations,
  dictionary,
}: {
  receiptCustomizations: SerializedReceiptCustomizations;
  dictionary: Dictionary;
}) {
  return (
    <ReceiptCustomizationTable
      columns={columns(dictionary)}
      initialData={receiptCustomizations}
    />
  );
}
