import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getReceiptCustomizations } from './actions';
import { ReceiptCustomizationsPageClient } from './page.client';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.receipt_customizations.title}`,
    description: dictionary.admin.receipt_customizations.description,
  };
}

const ReceiptCustomizationsPage = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<ReactElement> => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  const receiptCustomizations = await getReceiptCustomizations();

  return (
    <>
      <Header page={dictionary.admin.receipt_customizations.title} />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReceiptCustomizationsPageClient
            {...{ receiptCustomizations, dictionary }}
          />
        </div>
      </div>
    </>
  );
};

export default ReceiptCustomizationsPage;
