import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getReceiptCustomizations } from './actions';
import { columns } from './components/receipt-customization-column';
import { ReceiptCustomizationTable } from './components/receipt-customization-table';

const title = 'Senders Return - Receipt Customizations';
const description = 'View customer receipt customization requests';

export const metadata: Metadata = {
  title,
  description,
};

const ReceiptCustomizationsPage = async (): Promise<ReactElement> => {
  const receiptCustomizations = await getReceiptCustomizations();

  return (
    <>
      <Header page="Receipt Customizations" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReceiptCustomizationTable
            columns={columns}
            initialData={receiptCustomizations}
          />
        </div>
      </div>
    </>
  );
};

export default ReceiptCustomizationsPage;
