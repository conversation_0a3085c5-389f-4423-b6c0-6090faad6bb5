import type { Metadata } from 'next';
import { Header } from '../components/header';
import { ReturnScannerClient } from './return-scanner-client';

const title = 'Senders Return - Return Scanner';
const description = 'Scan Return tracking numbers to find return requests';

export const metadata: Metadata = {
  title,
  description,
};

const ReturnScannerPage = () => {
  return (
    <>
      <Header page="Return Scanner" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReturnScannerClient />
        </div>
      </div>
    </>
  );
};

export default ReturnScannerPage;
