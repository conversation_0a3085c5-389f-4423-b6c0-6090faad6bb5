'use server';

import prisma from '@repo/database/prisma-client';
import { log } from '@repo/observability/log';

export async function findReturnByReturnNumber(returnNumber: string) {
  log.info(
    `Searching for return request with tracking number: ${returnNumber}`
  );

  try {
    // Find return request by tracking number
    const returnRequest = await prisma.returnRequest.findFirst({
      where: {
        returnNumber,
      },
      select: {
        id: true,
        returnNumber: true,
        processed: true,
        exchangeType: true,
        orderName: true,
        email: true,
        returnItems: {
          select: {
            id: true,
            title: true,
            sku: true,
            barcode: true,
            quantity: true,
          },
        },
      },
    });

    return returnRequest;
  } catch (error) {
    log.error('Error searching for return request:', { error });
    return null;
  }
}

export async function updateReturnRequestStatus(
  returnRequestId: string,
  processed: string
) {
  try {
    const updatedRequest = await prisma.returnRequest.update({
      where: { id: returnRequestId },
      data: { processed },
    });

    log.info(
      `Updated return request ${returnRequestId} status to ${processed}`
    );
    return updatedRequest;
  } catch (error) {
    log.error('Error updating return request status:', { error });
    throw new Error('Failed to update return request status');
  }
}
